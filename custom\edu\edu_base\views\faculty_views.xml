<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Enhanced List View -->
    <record id="view_faculty_list" model="ir.ui.view">
        <field name="name">edu.faculty.list</field>
        <field name="model">edu.faculty</field>
        <field name="arch" type="xml">
            <list string="Faculty" default_order="name" multi_edit="1">
                <field name="image_128" string="Photo" widget="image" options="{'size': [40, 40]}"/>
                <field name="employee_id" string="Employee ID"/>
                <field name="name" string="Full Name"/>
                <field name="display_name" string="Display Name" optional="hide"/>
                <field name="designation" string="Designation"/>
                <field name="department_id" string="Department" optional="show"/>
                <field name="employment_type" string="Employment Type" optional="show"/>
                <field name="employment_status" string="Status" optional="show"/>
                <field name="current_teaching_hours" string="Teaching Hours" optional="show"/>
                <field name="teaching_load_percentage" string="Load %" optional="show"/>
                <field name="student_count" string="Students" optional="show"/>
                <field name="years_of_service" string="Years of Service" optional="show"/>
                <field name="state" string="Status" widget="badge" 
                       decoration-success="state == 'active'"
                       decoration-info="state == 'on_leave'"
                       decoration-warning="state == 'inactive'"
                       decoration-danger="state in ['resigned', 'terminated']"
                       decoration-muted="state == 'retired'"/>
                <field name="joining_date" string="Joining Date" optional="hide"/>
                <field name="email" string="Email" optional="hide"/>
                <field name="phone" string="Phone" optional="hide"/>
                <field name="company_id" string="Institution" groups="base.group_multi_company" optional="hide"/>
                <field name="active" invisible="1"/>
            </list>
        </field>
    </record>

    <!-- Enhanced Form View -->
    <record id="view_faculty_form" model="ir.ui.view">
        <field name="name">edu.faculty.form</field>
        <field name="model">edu.faculty</field>
        <field name="arch" type="xml">
            <form string="Faculty">
                <header>
                    <button name="action_activate" string="Activate" type="object" 
                            class="oe_highlight" invisible="state == 'active'"/>
                    <button name="action_deactivate" string="Deactivate" type="object" 
                            invisible="state != 'active'"/>
                    <button name="action_put_on_leave" string="Put on Leave" type="object" 
                            invisible="state != 'active'"/>
                    <button name="action_resign" string="Resign" type="object" 
                            invisible="state in ['resigned', 'retired', 'terminated']" 
                            confirm="Are you sure you want to process resignation?"/>
                    <button name="action_retire" string="Retire" type="object" 
                            invisible="state in ['resigned', 'retired', 'terminated']" 
                            confirm="Are you sure you want to process retirement?"/>
                    <button name="action_terminate" string="Terminate" type="object" 
                            invisible="state in ['resigned', 'retired', 'terminated']" 
                            confirm="Are you sure you want to terminate this faculty?"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,active,on_leave,inactive"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_subject_allocations" type="object" class="oe_stat_button" icon="fa-book">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_text">Subject Allocations</span>
                            </div>
                        </button>
                        <button name="action_view_students" type="object" class="oe_stat_button" icon="fa-users">
                            <field name="student_count" widget="statinfo" string="Students"/>
                        </button>
                        <button name="action_create_user_account" type="object" class="oe_stat_button" 
                                icon="fa-user" invisible="user_id">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_text">Create User</span>
                            </div>
                        </button>
                        <button name="action_generate_faculty_card" type="object" class="oe_stat_button" icon="fa-id-card">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_text">Faculty Card</span>
                            </div>
                        </button>
                        <button name="action_performance_appraisal" type="object" class="oe_stat_button" icon="fa-star">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_text">Appraisal</span>
                            </div>
                        </button>
                        <button name="action_view_documents" type="object" class="oe_stat_button" icon="fa-files-o">
                            <field name="document_count" widget="statinfo" string="Documents"/>
                        </button>
                    </div>
                    
                    <field name="image_1920" widget="image" class="oe_avatar" 
                           options="{'preview_image': 'image_128', 'size': [150, 150]}"/>
                    
                    <div class="oe_title">
                        <label for="name" class="oe_edit_only"/>
                        <h1>
                            <field name="name" placeholder="Faculty Full Name" required="1"/>
                        </h1>
                        <h3>
                            <field name="employee_id" placeholder="Employee ID" readonly="1"/>
                        </h3>
                    </div>

                    <group>
                        <group name="basic_info" string="Basic Information">
                            <field name="display_name" readonly="1"/>
                            <field name="designation" required="1"/>
                            <field name="department_id"/>
                            <field name="employment_type" required="1"/>
                            <field name="employment_status"/>
                            <field name="joining_date" required="1"/>
                            <field name="confirmation_date"/>
                        </group>
                        <group name="academic_info" string="Academic Information">
                            <field name="highest_qualification"/>
                            <field name="specialization"/>
                            <field name="total_experience_years"/>
                            <field name="teaching_experience_years"/>
                            <field name="industry_experience_years"/>
                            <field name="research_experience_years"/>
                        </group>
                    </group>

                    <notebook>
                        <page string="Contact Information" name="contact_info">
                            <group>
                                <group string="Address" name="address">
                                    <field name="street" placeholder="Street..."/>
                                    <field name="street2" placeholder="Street 2..."/>
                                    <field name="city" placeholder="City"/>
                                    <field name="state_id" placeholder="State" options="{'no_create': True}"/>
                                    <field name="zip" placeholder="ZIP"/>
                                    <field name="country_id" placeholder="Country" options="{'no_create': True}"/>
                                </group>
                                <group string="Communication" name="communication">
                                    <field name="email" widget="email"/>
                                    <field name="phone" widget="phone"/>
                                    <field name="mobile" widget="phone"/>
                                    <field name="office_phone" widget="phone"/>
                                    <field name="website" widget="url"/>
                                </group>
                            </group>
                            <group>
                                <group string="Emergency Contact" name="emergency_contact">
                                    <field name="emergency_contact_name"/>
                                    <field name="emergency_contact_phone" widget="phone"/>
                                    <field name="emergency_contact_relation"/>
                                    <field name="emergency_contact_address"/>
                                </group>
                                <group string="Office Information" name="office_info">
                                    <field name="office_location"/>
                                    <field name="cabin_number"/>
                                    <field name="office_hours"/>
                                </group>
                            </group>
                        </page>

                        <page string="Academic &amp; Research" name="academic_research">
                            <group>
                                <group string="Qualifications" name="qualifications">
                                    <field name="qualifications" nolabel="1" placeholder="List all educational qualifications..."/>
                                </group>
                                <group string="Research Interests" name="research">
                                    <field name="research_interests" nolabel="1" placeholder="Areas of research interest..."/>
                                </group>
                            </group>
                            <separator string="Publications"/>
                            <field name="publications" nolabel="1" placeholder="List of publications, papers, and research work..."/>
                        </page>

                        <page string="Teaching Information" name="teaching_info">
                            <group>
                                <group string="Teaching Load" name="teaching_load">
                                    <field name="max_teaching_hours_per_week"/>
                                    <field name="current_teaching_hours" readonly="1"/>
                                    <field name="teaching_load_percentage" readonly="1"/>
                                </group>
                                <group string="Class Teacher Info" name="class_teacher">
                                    <field name="is_class_teacher"/>
                                    <field name="class_teacher_section_ids" widget="many2many_tags" 
                                           invisible="not is_class_teacher"/>
                                </group>
                            </group>
                            <group>
                                <group string="Subjects Can Teach" name="subjects">
                                    <field name="subject_ids" nolabel="1" widget="many2many_tags"/>
                                </group>
                                <group string="Primary Subjects" name="primary_subjects">
                                    <field name="primary_subjects" nolabel="1" widget="many2many_tags"/>
                                </group>
                            </group>
                            <group>
                                <group string="Assigned Sections" name="sections">
                                    <field name="section_ids" nolabel="1" widget="many2many_tags"/>
                                </group>
                                <group string="Teaching Metrics" name="metrics">
                                    <field name="student_count" readonly="1"/>
                                    <field name="section_count" readonly="1"/>
                                    <field name="subject_count" readonly="1"/>
                                </group>
                            </group>
                        </page>

                        <page string="Administrative Roles" name="admin_roles">
                            <group>
                                <group string="Leadership Roles" name="leadership">
                                    <field name="is_department_head"/>
                                    <field name="is_academic_coordinator"/>
                                    <field name="is_examination_coordinator"/>
                                </group>
                                <group string="Additional Responsibilities" name="additional">
                                    <field name="administrative_roles" nolabel="1" 
                                           placeholder="Additional administrative responsibilities..."/>
                                </group>
                            </group>
                        </page>

                        <page string="Employment Details" name="employment">
                            <group>
                                <group string="Employment Dates" name="employment_dates">
                                    <field name="resignation_date"/>
                                    <field name="retirement_date"/>
                                    <field name="last_working_date"/>
                                </group>
                                <group string="Service Information" name="service_info">
                                    <field name="years_of_service" readonly="1"/>
                                    <field name="months_to_retirement" readonly="1"/>
                                    <field name="is_eligible_for_promotion" readonly="1"/>
                                </group>
                            </group>
                            <group>
                                <group string="Financial Information" name="financial">
                                    <field name="basic_salary"/>
                                    <field name="gross_salary"/>
                                    <field name="currency_id" invisible="1"/>
                                </group>
                                <group string="Leave Information" name="leave_info">
                                    <field name="annual_leave_days"/>
                                    <field name="sick_leave_days"/>
                                    <field name="casual_leave_days"/>
                                    <field name="leave_balance" readonly="1"/>
                                </group>
                            </group>
                        </page>

                        <page string="Performance &amp; Evaluation" name="performance">
                            <group>
                                <group string="Performance Rating" name="rating">
                                    <field name="performance_rating"/>
                                    <field name="last_appraisal_date"/>
                                    <field name="next_appraisal_date"/>
                                </group>
                                <group string="System Information" name="system_info">
                                    <field name="user_id" readonly="1"/>
                                    <field name="company_id" groups="base.group_multi_company"/>
                                    <field name="active"/>
                                </group>
                            </group>
                        </page>

                        <page string="Additional Information" name="additional_info">
                            <separator string="Notes"/>
                            <field name="notes" nolabel="1" placeholder="Additional notes about the faculty member..."/>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- Search View -->
    <record id="view_faculty_search" model="ir.ui.view">
        <field name="name">edu.faculty.search</field>
        <field name="model">edu.faculty</field>
        <field name="arch" type="xml">
            <search string="Faculty">
                <field name="name" string="Faculty Name" filter_domain="['|', ('name', 'ilike', self), ('employee_id', 'ilike', self)]"/>
                <field name="employee_id"/>
                <field name="email"/>
                <field name="phone"/>
                <field name="department_id"/>
                <field name="designation"/>
                <field name="subject_ids"/>
                
                <separator/>
                <filter string="Active Faculty" name="active_faculty" domain="[('state', '=', 'active')]"/>
                <filter string="On Leave" name="on_leave" domain="[('state', '=', 'on_leave')]"/>
                <filter string="Inactive" name="inactive" domain="[('state', '=', 'inactive')]"/>
                <filter string="Department Heads" name="department_heads" domain="[('is_department_head', '=', True)]"/>
                <filter string="Class Teachers" name="class_teachers" domain="[('is_class_teacher', '=', True)]"/>
                <filter string="Academic Coordinators" name="academic_coordinators" domain="[('is_academic_coordinator', '=', True)]"/>
                
                <separator/>
                <filter string="Full Time" name="full_time" domain="[('employment_type', '=', 'full_time')]"/>
                <filter string="Part Time" name="part_time" domain="[('employment_type', '=', 'part_time')]"/>
                <filter string="Contract" name="contract" domain="[('employment_type', '=', 'contract')]"/>
                
                <separator/>
                <filter string="Eligible for Promotion" name="eligible_promotion" domain="[('is_eligible_for_promotion', '=', True)]"/>
                <filter string="High Teaching Load" name="high_load" domain="[('teaching_load_percentage', '&gt;', 80)]"/>
                
                <separator/>
                <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                
                <group expand="0" string="Group By">
                    <filter string="Status" name="group_by_state" context="{'group_by': 'state'}"/>
                    <filter string="Department" name="group_by_department" context="{'group_by': 'department_id'}"/>
                    <filter string="Designation" name="group_by_designation" context="{'group_by': 'designation'}"/>
                    <filter string="Employment Type" name="group_by_employment_type" context="{'group_by': 'employment_type'}"/>
                    <filter string="Employment Status" name="group_by_employment_status" context="{'group_by': 'employment_status'}"/>
                    <filter string="Joining Date" name="group_by_joining_date" context="{'group_by': 'joining_date'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Kanban View -->
    <record id="view_faculty_kanban" model="ir.ui.view">
        <field name="name">edu.faculty.kanban</field>
        <field name="model">edu.faculty</field>
        <field name="arch" type="xml">
            <kanban default_group_by="department_id" class="o_kanban_small_column">
                <field name="id"/>
                <field name="name"/>
                <field name="employee_id"/>
                <field name="image_128"/>
                <field name="state"/>
                <field name="designation"/>
                <field name="department_id"/>
                <field name="employment_type"/>
                <field name="current_teaching_hours"/>
                <field name="teaching_load_percentage"/>
                <field name="student_count"/>
                <field name="is_department_head"/>
                <field name="is_class_teacher"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_card oe_kanban_global_click">
                            <div class="o_kanban_image">
                                <img t-att-src="kanban_image('edu.faculty', 'image_128', record.id.raw_value)" alt="Faculty"/>
                            </div>
                            <div class="oe_kanban_details">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings">
                                        <strong class="o_kanban_record_title">
                                            <field name="name"/>
                                        </strong>
                                        <br/>
                                        <small class="o_kanban_record_subtitle text-muted">
                                            <field name="employee_id"/>
                                        </small>
                                    </div>
                                </div>
                                <div class="o_kanban_record_body">
                                    <div class="row">
                                        <div class="col-12">
                                            <span class="fa fa-graduation-cap"/> <field name="designation"/>
                                        </div>
                                    </div>
                                    <div t-if="record.department_id.raw_value">
                                        <span class="fa fa-building"/> <field name="department_id"/>
                                    </div>
                                    <div class="row">
                                        <div class="col-6">
                                            <span class="fa fa-clock-o"/> <field name="current_teaching_hours"/>h
                                        </div>
                                        <div class="col-6">
                                            <span class="fa fa-users"/> <field name="student_count"/>
                                        </div>
                                    </div>
                                    <div t-if="record.teaching_load_percentage.raw_value">
                                        <div class="progress" style="height: 10px;">
                                            <div class="progress-bar" role="progressbar" 
                                                 t-att-style="'width: ' + record.teaching_load_percentage.raw_value + '%'"
                                                 t-att-aria-valuenow="record.teaching_load_percentage.raw_value"
                                                 aria-valuemin="0" aria-valuemax="100">
                                            </div>
                                        </div>
                                        <small>Load: <field name="teaching_load_percentage"/>%</small>
                                    </div>
                                </div>
                                <div class="o_kanban_record_bottom">
                                    <div class="oe_kanban_bottom_left">
                                        <span t-if="record.is_department_head.raw_value" class="fa fa-star text-warning" title="Department Head"/>
                                        <span t-if="record.is_class_teacher.raw_value" class="fa fa-chalkboard-teacher text-info" title="Class Teacher"/>
                                    </div>
                                    <div class="oe_kanban_bottom_right">
                                        <span t-att-class="'badge badge-' + (record.state.raw_value == 'active' ? 'success' : record.state.raw_value == 'on_leave' ? 'warning' : record.state.raw_value == 'inactive' ? 'secondary' : 'danger')">
                                            <field name="state"/>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- Calendar View -->
    <record id="view_faculty_calendar" model="ir.ui.view">
        <field name="name">edu.faculty.calendar</field>
        <field name="model">edu.faculty</field>
        <field name="arch" type="xml">
            <calendar string="Faculty" date_start="joining_date" color="department_id" mode="month">
                <field name="name"/>
                <field name="employee_id"/>
                <field name="designation"/>
                <field name="department_id"/>
                <field name="state"/>
            </calendar>
        </field>
    </record>

    <!-- Action -->
    <record id="action_edu_faculty" model="ir.actions.act_window">
        <field name="name">Faculty</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">edu.faculty</field>
        <field name="view_mode">kanban,list,form,calendar</field>
        <field name="context">{'search_default_active_faculty': 1}</field>
        <field name="help" type="html">
            <div class="o_view_nocontent_edu edu_fade_in">
                <!-- Welcome Section -->
                <div class="edu_help_welcome">
                    <h1 class="edu_help_title">👨‍🏫 Faculty Management</h1>
                    <p class="edu_help_subtitle">Great teachers inspire great minds!</p>
                    <p class="edu_help_tagline">
                        <strong>Empowering Educators</strong><br/>
                        Build your dream team of educators who will shape the next generation. Every faculty member is a cornerstone of academic excellence.
                    </p>
                </div>

                <!-- Quick Start Steps -->
                <div class="edu_progress_steps">
                    <div class="edu_step">
                        <div class="edu_step_icon">👨‍🏫</div>
                        <div class="edu_step_text">Add Faculty</div>
                    </div>
                    <div class="edu_step">
                        <div class="edu_step_icon">📜</div>
                        <div class="edu_step_text">Add Credentials</div>
                    </div>
                    <div class="edu_step">
                        <div class="edu_step_icon">📚</div>
                        <div class="edu_step_text">Assign Subjects</div>
                    </div>
                    <div class="edu_step">
                        <div class="edu_step_icon">📈</div>
                        <div class="edu_step_text">Track Performance</div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="edu_help_actions">
                    <a href="#" class="edu_action_btn btn-primary" onclick="this.createNewRecord()">
                        <i class="fa fa-plus"></i>
                        Add New Faculty
                    </a>
                    <a href="#" class="edu_action_btn btn-success" onclick="this.showQuickStart()">
                        <i class="fa fa-rocket"></i>
                        Quick Start Guide
                    </a>
                    <a href="/documentation/faculty-management" class="edu_action_btn btn-outline-secondary" target="_blank">
                        <i class="fa fa-book"></i>
                        Documentation
                    </a>
                </div>

                <!-- Enhanced Features Alert -->
                <div class="alert edu_alert_enhanced alert-success edu_slide_up">
                    <h5>
                        <i class="fa fa-bullseye"></i>
                        🎯 Manage your teaching excellence:
                    </h5>
                    <ul class="mb-0">
                        <li>👤 Comprehensive faculty profiles and qualifications</li>
                        <li>📚 Teaching assignments, subjects, and class schedules</li>
                        <li>🎓 Academic credentials, certifications, and research work</li>
                        <li>⚡ Administrative roles, committees, and leadership positions</li>
                        <li>📊 Performance reviews, feedback, and career development</li>
                        <li>🏆 Awards, recognitions, and professional achievements</li>
                        <li>📝 Research publications and academic contributions</li>
                        <li>⏰ Workload management and time tracking</li>
                    </ul>
                    <div class="mt-3">
                        <button class="btn btn-sm btn-outline-primary" onclick="this.copyTips()">
                            <i class="fa fa-copy"></i> Copy Tips
                        </button>
                        <button class="btn btn-sm btn-outline-info ml-2" onclick="this.showExamples()">
                            <i class="fa fa-lightbulb-o"></i> Examples
                        </button>
                        <button class="btn btn-sm btn-outline-success ml-2" onclick="this.importFaculty()">
                            <i class="fa fa-upload"></i> Import Faculty
                        </button>
                    </div>
                </div>

                <!-- Inspirational Quote -->
                <div class="edu_help_quote">
                    <em>"A good teacher can inspire hope, ignite the imagination, and instill a love of learning."</em>
                    <div class="text-right mt-2">
                        <small>- Brad Henry</small>
                    </div>
                </div>
            </div>
        </field>
    </record>

    <!-- Dashboard Actions -->
    <record id="action_active_faculty_dashboard" model="ir.actions.act_window">
        <field name="name">Active Faculty</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">edu.faculty</field>
        <field name="view_mode">kanban,list,form</field>
        <field name="domain">[('state', '=', 'active')]</field>
        <field name="context">{'create': False}</field>
    </record>

    <record id="action_department_heads" model="ir.actions.act_window">
        <field name="name">Department Heads</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">edu.faculty</field>
        <field name="view_mode">kanban,list,form</field>
        <field name="domain">[('is_department_head', '=', True)]</field>
        <field name="context">{'create': False}</field>
    </record>

    <record id="action_class_teachers" model="ir.actions.act_window">
        <field name="name">Class Teachers</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">edu.faculty</field>
        <field name="view_mode">kanban,list,form</field>
        <field name="domain">[('is_class_teacher', '=', True)]</field>
        <field name="context">{'create': False}</field>
    </record>

</odoo>