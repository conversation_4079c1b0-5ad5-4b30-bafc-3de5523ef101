<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <!-- List View -->
    <record id="view_edu_academic_term_list" model="ir.ui.view">
        <field name="name">edu.academic.term.list</field>
        <field name="model">edu.academic.term</field>
        <field name="arch" type="xml">
            <list string="Academic Terms">
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="code"/>
                <field name="academic_year_id"/>
                <field name="start_date"/>
                <field name="end_date"/>
                <field name="batch_count"/>
                <field name="state" decoration-info="state == 'draft'" 
                       decoration-success="state == 'active'"
                       decoration-muted="state == 'completed'"/>
            </list>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_edu_academic_term_form" model="ir.ui.view">
        <field name="name">edu.academic.term.form</field>
        <field name="model">edu.academic.term</field>
        <field name="arch" type="xml">
            <form string="Academic Term">
                <header>
                    <field name="state" widget="statusbar"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Term Name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="code"/>
                            <field name="academic_year_id"/>
                            <field name="sequence"/>
                        </group>
                        <group>
                            <field name="start_date"/>
                            <field name="end_date"/>
                            <field name="batch_count"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Academic Batches" name="academic_batches">
                            <field name="batch_ids"/>
                        </page>
                        <page string="Notes" name="notes">
                            <field name="notes"/>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- Search View -->
    <record id="view_edu_academic_term_search" model="ir.ui.view">
        <field name="name">edu.academic.term.search</field>
        <field name="model">edu.academic.term</field>
        <field name="arch" type="xml">
            <search string="Search Academic Terms">
                <field name="name"/>
                <field name="code"/>
                <field name="academic_year_id"/>
                <separator/>
                <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
                <filter string="Active" name="active" domain="[('state', '=', 'active')]"/>
                <filter string="Completed" name="completed" domain="[('state', '=', 'completed')]"/>
                <group expand="0" string="Group By">
                    <filter string="Academic Year" name="group_by_year" context="{'group_by': 'academic_year_id'}"/>
                    <filter string="Status" name="group_by_state" context="{'group_by': 'state'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action -->
    <record id="action_edu_academic_term" model="ir.actions.act_window">
        <field name="name">Academic Terms</field>
        <field name="res_model">edu.academic.term</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="view_edu_academic_term_search"/>
        <field name="help" type="html">
            <div class="o_view_nocontent_edu edu_fade_in">
                <!-- Welcome Section -->
                <div class="edu_help_welcome">
                    <h1 class="edu_help_title">📅 Academic Term Management</h1>
                    <p class="edu_help_subtitle">Time is the canvas of education!</p>
                    <p class="edu_help_tagline">
                        <strong>Structuring Academic Time</strong><br/>
                        Structure your academic year into meaningful periods that create rhythm and progression in learning. Each term is a chapter in your students' educational story.
                    </p>
                </div>

                <!-- Quick Start Steps -->
                <div class="edu_progress_steps">
                    <div class="edu_step">
                        <div class="edu_step_icon">📅</div>
                        <div class="edu_step_text">Create Term</div>
                    </div>
                    <div class="edu_step">
                        <div class="edu_step_icon">⏰</div>
                        <div class="edu_step_text">Set Schedule</div>
                    </div>
                    <div class="edu_step">
                        <div class="edu_step_icon">📚</div>
                        <div class="edu_step_text">Plan Courses</div>
                    </div>
                    <div class="edu_step">
                        <div class="edu_step_icon">🎯</div>
                        <div class="edu_step_text">Track Progress</div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="edu_help_actions">
                    <a href="#" class="edu_action_btn btn-primary" onclick="this.createNewRecord()">
                        <i class="fa fa-plus"></i>
                        Create New Term
                    </a>
                    <a href="#" class="edu_action_btn btn-success" onclick="this.showQuickStart()">
                        <i class="fa fa-rocket"></i>
                        Quick Start Guide
                    </a>
                    <a href="/documentation/term-management" class="edu_action_btn btn-outline-secondary" target="_blank">
                        <i class="fa fa-book"></i>
                        Documentation
                    </a>
                </div>

                <!-- Enhanced Features Alert -->
                <div class="alert edu_alert_enhanced alert-warning edu_slide_up">
                    <h5>
                        <i class="fa fa-bullseye"></i>
                        ⏰ What you can organize:
                    </h5>
                    <ul class="mb-0">
                        <li>📊 Semester, quarter, or trimester divisions</li>
                        <li>📚 Course scheduling and academic planning</li>
                        <li>📝 Assessment periods and examination schedules</li>
                        <li>🎯 Learning milestones and progress tracking</li>
                        <li>📋 Registration periods and enrollment windows</li>
                        <li>🏆 Term-based achievements and recognitions</li>
                        <li>📈 Performance analytics and reporting</li>
                        <li>🔄 Term transitions and continuity</li>
                    </ul>
                    <div class="mt-3">
                        <button class="btn btn-sm btn-outline-primary" onclick="this.copyTips()">
                            <i class="fa fa-copy"></i> Copy Tips
                        </button>
                        <button class="btn btn-sm btn-outline-info ml-2" onclick="this.showExamples()">
                            <i class="fa fa-lightbulb-o"></i> Examples
                        </button>
                    </div>
                </div>

                <!-- Inspirational Quote -->
                <div class="edu_help_quote">
                    <em>"Time is what we want most, but what we use worst."</em>
                    <div class="text-right mt-2">
                        <small>- William Penn</small>
                    </div>
                </div>
            </div>
        </field>
    </record>
</odoo>