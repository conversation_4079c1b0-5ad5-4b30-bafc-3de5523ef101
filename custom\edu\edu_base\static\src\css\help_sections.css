/* Enhanced Help Sections for EDU Base Module */

/* Main Help Container */
.o_view_nocontent_edu {
    padding: 40px 20px;
    text-align: center;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    margin: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    position: relative;
    overflow: hidden;
}

.o_view_nocontent_edu::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #007bff, #28a745, #ffc107, #dc3545);
}

/* Welcome Section */
.edu_help_welcome {
    margin-bottom: 30px;
}

.edu_help_title {
    font-size: 2.5rem;
    margin-bottom: 10px;
    color: #2c3e50;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.edu_help_subtitle {
    font-size: 1.2rem;
    color: #6c757d;
    margin-bottom: 20px;
    font-weight: 500;
}

.edu_help_tagline {
    font-size: 1.1rem;
    color: #495057;
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto 30px;
}

/* Enhanced Alert Boxes */
.edu_alert_enhanced {
    border: none;
    border-radius: 12px;
    padding: 25px;
    margin: 25px auto;
    max-width: 700px;
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.edu_alert_enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 5px;
    height: 100%;
}

.edu_alert_enhanced.alert-primary::before {
    background: #007bff;
}

.edu_alert_enhanced.alert-success::before {
    background: #28a745;
}

.edu_alert_enhanced.alert-info::before {
    background: #17a2b8;
}

.edu_alert_enhanced.alert-warning::before {
    background: #ffc107;
}

.edu_alert_enhanced h5 {
    font-size: 1.3rem;
    margin-bottom: 15px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.edu_alert_enhanced ul {
    list-style: none;
    padding-left: 0;
}

.edu_alert_enhanced li {
    padding: 8px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.edu_alert_enhanced li:last-child {
    border-bottom: none;
}

.edu_alert_enhanced li:hover {
    background-color: rgba(0, 0, 0, 0.02);
    padding-left: 10px;
    border-radius: 6px;
}

/* Action Buttons */
.edu_help_actions {
    margin: 30px 0;
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

.edu_action_btn {
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.edu_action_btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    text-decoration: none;
}

.edu_action_btn.btn-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
}

.edu_action_btn.btn-success {
    background: linear-gradient(135deg, #28a745, #1e7e34);
    color: white;
}

.edu_action_btn.btn-outline-secondary {
    background: white;
    color: #6c757d;
    border: 2px solid #dee2e6;
}

/* Quote Section */
.edu_help_quote {
    margin-top: 40px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 10px;
    border-left: 4px solid #007bff;
    font-style: italic;
    color: #6c757d;
    position: relative;
}

.edu_help_quote::before {
    content: '"';
    font-size: 4rem;
    color: #dee2e6;
    position: absolute;
    top: -10px;
    left: 20px;
    font-family: serif;
}

/* Progress Indicators */
.edu_progress_steps {
    display: flex;
    justify-content: center;
    margin: 30px 0;
    gap: 20px;
    flex-wrap: wrap;
}

.edu_step {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    max-width: 120px;
}

.edu_step_icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-bottom: 10px;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.edu_step_text {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
    .o_view_nocontent_edu {
        margin: 10px;
        padding: 30px 15px;
    }
    
    .edu_help_title {
        font-size: 2rem;
    }
    
    .edu_help_actions {
        flex-direction: column;
        align-items: center;
    }
    
    .edu_action_btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }
    
    .edu_progress_steps {
        flex-direction: column;
        align-items: center;
    }
}

/* Animation Classes */
.edu_fade_in {
    animation: eduFadeIn 0.6s ease-out;
}

@keyframes eduFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.edu_slide_up {
    animation: eduSlideUp 0.8s ease-out;
}

@keyframes eduSlideUp {
    from {
        opacity: 0;
        transform: translateY(40px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Accessibility Improvements */
.edu_help_section {
    outline: none;
}

.edu_help_section:focus-within {
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
}

.edu_action_btn:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .o_view_nocontent_edu {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        color: #ecf0f1;
    }
    
    .edu_help_title {
        color: #ecf0f1;
    }
    
    .edu_help_quote {
        background: rgba(52, 73, 94, 0.7);
        color: #bdc3c7;
        border-left-color: #3498db;
    }
}
