# -*- coding: utf-8 -*-

from odoo import models, api
from markupsafe import Markup


class EduHelpContentGenerator(models.AbstractModel):
    """
    Abstract model for generating enhanced help content for EDU Base views
    Provides standardized help sections with consistent styling and content
    """
    _name = 'edu.help.content.generator'
    _description = 'EDU Help Content Generator'

    # Alert type constants
    ALERT_PRIMARY = 'primary'
    ALERT_SUCCESS = 'success'
    ALERT_INFO = 'info'
    ALERT_WARNING = 'warning'
    ALERT_SECONDARY = 'secondary'

    # Common emoji sets
    EMOJIS = {
        'academic': ['📚', '🎓', '📖', '✏️', '📝', '🏫'],
        'people': ['👨‍🏫', '👩‍🎓', '👥', '👤', '👨‍👩‍👧‍👦'],
        'management': ['📊', '📋', '🗂️', '📁', '🏛️', '⚙️'],
        'success': ['🏆', '🌟', '✨', '🎯', '💫', '🚀'],
        'time': ['📅', '⏰', '📆', '⏱️', '🕐'],
        'location': ['🏫', '🏛️', '🏢', '🌍', '📍']
    }

    @api.model
    def generate_help_content(self, config):
        """
        Generate enhanced help content HTML based on configuration
        
        Args:
            config (dict): Configuration dictionary with help content details
            
        Returns:
            Markup: Safe HTML markup for the help section
        """
        # Default configuration
        default_config = {
            'title': '📋 Management System',
            'subtitle': 'Welcome to the management interface!',
            'tagline': 'Efficient Management',
            'description': 'Manage your records efficiently with our comprehensive tools and features.',
            'alert_type': self.ALERT_PRIMARY,
            'record_type': 'Record',
            'features': [
                '📝 Create and manage records',
                '🔍 Search and filter capabilities',
                '📊 Reporting and analytics',
                '⚙️ Configuration and settings'
            ],
            'quote': 'Success is where preparation and opportunity meet. - Bobby Unser',
            'show_actions': True,
            'show_quote': True,
            'custom_css_class': ''
        }
        
        # Merge with provided config
        config = {**default_config, **config}
        
        # Generate HTML
        html_content = self._build_help_html(config)
        
        return Markup(html_content)

    def _build_help_html(self, config):
        """Build the complete help HTML structure"""
        
        css_classes = f"o_view_nocontent_edu edu_fade_in {config.get('custom_css_class', '')}"
        
        html = f'''
        <div class="{css_classes}">
            {self._build_welcome_section(config)}
            {self._build_action_buttons(config) if config.get('show_actions') else ''}
            {self._build_features_alert(config)}
            {self._build_quote_section(config) if config.get('show_quote') and config.get('quote') else ''}
        </div>
        '''
        
        return html

    def _build_welcome_section(self, config):
        """Build the welcome section HTML"""
        return f'''
        <div class="edu_help_welcome">
            <h1 class="edu_help_title">{config['title']}</h1>
            <p class="edu_help_subtitle">{config['subtitle']}</p>
            <p class="edu_help_tagline">
                <strong>{config['tagline']}</strong><br/>
                {config['description']}
            </p>
        </div>
        '''

    def _build_action_buttons(self, config):
        """Build the action buttons section"""
        record_type = config.get('record_type', 'Record')
        
        return f'''
        <div class="edu_help_actions">
            <button class="edu_action_btn btn-primary" onclick="this.createNewRecord()">
                <i class="fa fa-plus"></i>
                Create New {record_type}
            </button>
            <button class="edu_action_btn btn-success" onclick="this.showQuickStart()">
                <i class="fa fa-rocket"></i>
                Quick Start Guide
            </button>
            <button class="edu_action_btn btn-outline-secondary" onclick="this.openDocumentation()">
                <i class="fa fa-book"></i>
                Documentation
            </button>
        </div>
        '''

    def _build_features_alert(self, config):
        """Build the features alert box"""
        alert_type = config.get('alert_type', self.ALERT_INFO)
        features_html = ''.join([f'<li>{feature}</li>' for feature in config.get('features', [])])
        
        return f'''
        <div class="alert edu_alert_enhanced alert-{alert_type} edu_slide_up">
            <h5>
                <i class="fa fa-bullseye"></i>
                What you can manage here:
            </h5>
            <ul class="mb-0">
                {features_html}
            </ul>
            <div class="mt-3">
                <button class="btn btn-sm btn-outline-primary" onclick="this.copyTips()">
                    <i class="fa fa-copy"></i> Copy Tips
                </button>
                <button class="btn btn-sm btn-outline-info ml-2" onclick="this.showExamples()">
                    <i class="fa fa-lightbulb-o"></i> Examples
                </button>
            </div>
        </div>
        '''

    def _build_quote_section(self, config):
        """Build the inspirational quote section"""
        quote = config.get('quote', '')
        if not quote:
            return ''
            
        return f'''
        <div class="edu_help_quote">
            <em>{quote}</em>
        </div>
        '''

    @api.model
    def get_student_help_content(self):
        """Get help content for student management"""
        config = {
            'title': '🎓 Student Management',
            'subtitle': 'Every great journey begins with a single step!',
            'tagline': 'Building Tomorrow\'s Leaders',
            'description': 'Start building your educational community by adding your first student. This is where dreams take shape and futures are molded.',
            'alert_type': self.ALERT_INFO,
            'record_type': 'Student',
            'features': [
                '📝 Complete student profiles with photos and personal details',
                '🎯 Academic enrollment, grades, and performance tracking',
                '🏥 Health records, allergies, and special accommodation needs',
                '👨‍👩‍👧‍👦 Guardian contacts and emergency information',
                '🌍 International student documentation and visa status',
                '🏆 Achievements, awards, and extracurricular activities'
            ],
            'quote': 'Education is the most powerful weapon which you can use to change the world. - Nelson Mandela'
        }
        return self.generate_help_content(config)

    @api.model
    def get_faculty_help_content(self):
        """Get help content for faculty management"""
        config = {
            'title': '👨‍🏫 Faculty Management',
            'subtitle': 'Great teachers inspire great minds!',
            'tagline': 'Empowering Educators',
            'description': 'Build your dream team of educators who will shape the next generation. Every faculty member is a cornerstone of academic excellence.',
            'alert_type': self.ALERT_SUCCESS,
            'record_type': 'Faculty',
            'features': [
                '👤 Comprehensive faculty profiles and qualifications',
                '📚 Teaching assignments, subjects, and class schedules',
                '🎓 Academic credentials, certifications, and research work',
                '⚡ Administrative roles, committees, and leadership positions',
                '📊 Performance reviews, feedback, and career development',
                '🏆 Awards, recognitions, and professional achievements'
            ],
            'quote': 'A good teacher can inspire hope, ignite the imagination, and instill a love of learning. - Brad Henry'
        }
        return self.generate_help_content(config)

    @api.model
    def get_academic_year_help_content(self):
        """Get help content for academic year management"""
        config = {
            'title': '📅 Academic Year Management',
            'subtitle': 'Time to structure your academic journey!',
            'tagline': 'Organizing Academic Excellence',
            'description': 'Academic years are the foundation of your educational calendar. Set up your first year to begin organizing terms, semesters, and academic milestones.',
            'alert_type': self.ALERT_PRIMARY,
            'record_type': 'Academic Year',
            'features': [
                '🗓️ Academic calendar setup and milestone planning',
                '📊 Term and semester organization',
                '🎯 Academic goal setting and tracking',
                '📈 Performance analytics and reporting',
                '🔄 Year-end processes and transitions',
                '📋 Compliance and accreditation management'
            ],
            'quote': 'The future belongs to those who prepare for it today. - Malcolm X'
        }
        return self.generate_help_content(config)

    @api.model
    def get_course_help_content(self):
        """Get help content for course management"""
        config = {
            'title': '📚 Course Management',
            'subtitle': 'Knowledge begins with a single course!',
            'tagline': 'Designing Learning Experiences',
            'description': 'Design the building blocks of education by creating comprehensive courses that will inspire and educate students across different standards and levels.',
            'alert_type': self.ALERT_INFO,
            'record_type': 'Course',
            'features': [
                '📝 Detailed course descriptions and learning objectives',
                '🎯 Curriculum mapping and competency frameworks',
                '📊 Assessment strategies and evaluation methods',
                '🔗 Prerequisites and course sequencing',
                '📚 Resource allocation and material management',
                '👥 Faculty assignment and teaching coordination'
            ],
            'quote': 'Education is not preparation for life; education is life itself. - John Dewey'
        }
        return self.generate_help_content(config)

    @api.model
    def get_department_help_content(self):
        """Get help content for department management"""
        config = {
            'title': '🏛️ Department Management',
            'subtitle': 'Building academic excellence, one department at a time!',
            'tagline': 'Organizing Academic Excellence',
            'description': 'Create the organizational backbone of your institution. Departments are where knowledge domains come alive and academic communities thrive.',
            'alert_type': self.ALERT_WARNING,
            'record_type': 'Department',
            'features': [
                '🏗️ Department structure, hierarchy, and leadership roles',
                '👥 Faculty assignments, workload distribution, and team management',
                '📖 Academic programs, curriculum design, and subject coordination',
                '💰 Budget planning, allocation, and financial oversight',
                '🏅 Accreditation status, compliance, and quality assurance',
                '🔬 Research initiatives, labs, and infrastructure management'
            ],
            'quote': 'Excellence is never an accident. It is always the result of high intention, sincere effort, and intelligent execution. - Aristotle'
        }
        return self.generate_help_content(config)
