<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="view_edu_category_list" model="ir.ui.view">
        <field name="name">edu.category.list</field>
        <field name="model">edu.category</field>
        <field name="arch" type="xml">
            <list string="Educational Categories">
                <field name="name"/>
                <field name="code"/>
                <field name="reservation_percentage"/>
                <field name="description" optional="hide"/>
                <field name="active" optional="hide"/>
            </list>
        </field>
    </record>

    <record id="view_edu_category_form" model="ir.ui.view">
        <field name="name">edu.category.form</field>
        <field name="model">edu.category</field>
        <field name="arch" type="xml">
            <form string="Educational Category">
                <sheet>
                    <field name="active" invisible="1"/>
                    <widget name="web_ribbon" title="Archived" invisible="active"/>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="code"/>
                        </group>
                        <group>
                            <field name="reservation_percentage"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Description" name="description">
                            <field name="description"/>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <record id="view_edu_category_search" model="ir.ui.view">
        <field name="name">edu.category.search</field>
        <field name="model">edu.category</field>
        <field name="arch" type="xml">
            <search string="Educational Categories">
                <field name="name"/>
                <field name="code"/>
                <field name="description"/>
                <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
            </search>
        </field>
    </record>

    <record id="action_edu_category" model="ir.actions.act_window">
        <field name="name">Educational Categories</field>
        <field name="res_model">edu.category</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="view_edu_category_search"/>
        <field name="help" type="html">
            <div class="o_view_nocontent_edu edu_fade_in">
                <!-- Welcome Section -->
                <div class="edu_help_welcome">
                    <h1 class="edu_help_title">🏷️ Category Management</h1>
                    <p class="edu_help_subtitle">Organization starts with smart categorization!</p>
                    <p class="edu_help_tagline">
                        <strong>Structuring Information</strong><br/>
                        Create meaningful categories that help organize and classify your educational content, making everything easier to find and manage.
                    </p>
                </div>

                <!-- Quick Start Steps -->
                <div class="edu_progress_steps">
                    <div class="edu_step">
                        <div class="edu_step_icon">🏷️</div>
                        <div class="edu_step_text">Create Category</div>
                    </div>
                    <div class="edu_step">
                        <div class="edu_step_icon">📋</div>
                        <div class="edu_step_text">Define Rules</div>
                    </div>
                    <div class="edu_step">
                        <div class="edu_step_icon">🔗</div>
                        <div class="edu_step_text">Link Content</div>
                    </div>
                    <div class="edu_step">
                        <div class="edu_step_icon">📊</div>
                        <div class="edu_step_text">Monitor Usage</div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="edu_help_actions">
                    <a href="#" class="edu_action_btn btn-primary" onclick="this.createNewRecord()">
                        <i class="fa fa-plus"></i>
                        Create New Category
                    </a>
                    <a href="#" class="edu_action_btn btn-success" onclick="this.showQuickStart()">
                        <i class="fa fa-rocket"></i>
                        Quick Start Guide
                    </a>
                    <a href="/documentation/category-management" class="edu_action_btn btn-outline-secondary" target="_blank">
                        <i class="fa fa-book"></i>
                        Documentation
                    </a>
                </div>

                <!-- Enhanced Features Alert -->
                <div class="alert edu_alert_enhanced alert-info edu_slide_up">
                    <h5>
                        <i class="fa fa-bullseye"></i>
                        📂 What you can organize:
                    </h5>
                    <ul class="mb-0">
                        <li>📚 Subject areas and academic disciplines</li>
                        <li>🎯 Learning objectives and skill categories</li>
                        <li>📊 Assessment types and evaluation methods</li>
                        <li>🏆 Achievement levels and performance tiers</li>
                        <li>🔍 Search filters and content classification</li>
                        <li>📋 Administrative groupings and workflows</li>
                        <li>🏷️ Tagging systems and metadata</li>
                        <li>📈 Usage analytics and optimization</li>
                    </ul>
                    <div class="mt-3">
                        <button class="btn btn-sm btn-outline-primary" onclick="this.copyTips()">
                            <i class="fa fa-copy"></i> Copy Tips
                        </button>
                        <button class="btn btn-sm btn-outline-info ml-2" onclick="this.showExamples()">
                            <i class="fa fa-lightbulb-o"></i> Examples
                        </button>
                    </div>
                </div>

                <!-- Inspirational Quote -->
                <div class="edu_help_quote">
                    <em>"For every minute spent organizing, an hour is earned."</em>
                    <div class="text-right mt-2">
                        <small>- Benjamin Franklin</small>
                    </div>
                </div>
            </div>
        </field>
    </record>
</odoo>