<?xml version="1.0" encoding="UTF-8"?>
<templates>
    <!-- Enhanced Help Section Template -->
    <t t-name="edu_base.HelpSectionTemplate">
        <div class="o_view_nocontent_edu edu_fade_in" role="region" aria-label="Help and guidance section">
            <!-- Welcome Section -->
            <div class="edu_help_welcome">
                <h1 class="edu_help_title">
                    <t t-esc="props.title"/>
                </h1>
                <p class="edu_help_subtitle">
                    <t t-esc="props.subtitle"/>
                </p>
                <p class="edu_help_tagline">
                    <strong><t t-esc="props.tagline"/></strong><br/>
                    <t t-esc="props.description"/>
                </p>
            </div>

            <!-- Quick Start Steps -->
            <div class="edu_progress_steps" t-if="props.quickSteps">
                <div class="edu_step" t-foreach="props.quickSteps" t-as="step" t-key="step_index">
                    <div class="edu_step_icon">
                        <t t-esc="step.icon"/>
                    </div>
                    <div class="edu_step_text">
                        <t t-esc="step.text"/>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="edu_help_actions">
                <button class="edu_action_btn btn-primary" t-on-click="createNewRecord" 
                        aria-label="Create new record">
                    <i class="fa fa-plus"></i>
                    Create New <t t-esc="props.recordType || 'Record'"/>
                </button>
                <button class="edu_action_btn btn-success" t-on-click="toggleQuickStart"
                        aria-label="Start quick guide">
                    <i class="fa fa-rocket"></i>
                    Quick Start Guide
                </button>
                <button class="edu_action_btn btn-outline-secondary" t-on-click="openDocumentation"
                        aria-label="Open documentation">
                    <i class="fa fa-book"></i>
                    Documentation
                </button>
            </div>

            <!-- Features Alert Box -->
            <div t-attf-class="alert edu_alert_enhanced alert-{{props.alertType || 'info'}} edu_slide_up">
                <h5>
                    <i class="fa fa-bullseye"></i>
                    <t t-esc="props.featuresTitle || 'What you can manage here:'"/>
                </h5>
                <ul class="mb-0">
                    <li t-foreach="props.features" t-as="feature" t-key="feature_index"
                        t-on-click="() => this.highlightFeature(feature_index)"
                        role="button" tabindex="0">
                        <t t-esc="feature"/>
                    </li>
                </ul>
                <div class="mt-3">
                    <button class="btn btn-sm btn-outline-primary" t-on-click="copyTips"
                            aria-label="Copy tips to clipboard">
                        <i class="fa fa-copy"></i> Copy Tips
                    </button>
                    <button class="btn btn-sm btn-outline-info ml-2" t-on-click="showExamples"
                            aria-label="Show examples">
                        <i class="fa fa-lightbulb-o"></i> Examples
                    </button>
                </div>
            </div>

            <!-- Inspirational Quote -->
            <div class="edu_help_quote" t-if="props.quote">
                <t t-esc="props.quote"/>
            </div>

            <!-- Quick Start Guide Modal -->
            <div class="modal fade" t-if="state.showQuickStart" tabindex="-1" role="dialog">
                <div class="modal-dialog modal-lg" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fa fa-rocket"></i>
                                Quick Start Guide
                            </h5>
                            <button type="button" class="close" t-on-click="toggleQuickStart">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6" t-foreach="props.quickSteps" t-as="step" t-key="step_index">
                                    <div class="card mb-3">
                                        <div class="card-body text-center">
                                            <div class="display-4 mb-3">
                                                <t t-esc="step.icon"/>
                                            </div>
                                            <h6 class="card-title">
                                                Step <t t-esc="step_index + 1"/>: <t t-esc="step.text"/>
                                            </h6>
                                            <p class="card-text text-muted">
                                                <t t-esc="step.description || 'Follow this step to get started'"/>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-primary" t-on-click="createNewRecord">
                                <i class="fa fa-plus"></i>
                                Get Started
                            </button>
                            <button type="button" class="btn btn-secondary" t-on-click="toggleQuickStart">
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </t>

    <!-- Compact Help Section Template -->
    <t t-name="edu_base.CompactHelpTemplate">
        <div class="o_view_nocontent">
            <div class="o_nocontent_help">
                <p class="o_view_nocontent_smiling_face">
                    <t t-esc="props.emoji"/> <t t-esc="props.title"/>
                </p>
                <p>
                    <strong><t t-esc="props.tagline"/></strong><br/>
                    <t t-esc="props.description"/>
                </p>
                <div class="mt-3">
                    <button class="btn btn-primary" t-on-click="createNewRecord">
                        <i class="fa fa-plus"></i>
                        Create <t t-esc="props.recordType"/>
                    </button>
                </div>
            </div>
        </div>
    </t>

    <!-- Feature List Template -->
    <t t-name="edu_base.FeatureListTemplate">
        <div t-attf-class="alert alert-{{props.alertType || 'info'}}">
            <h5>
                <i t-attf-class="fa fa-{{props.icon || 'bullseye'}}"></i>
                <t t-esc="props.title"/>
            </h5>
            <ul class="mb-0">
                <li t-foreach="props.features" t-as="feature" t-key="feature_index">
                    <t t-esc="feature"/>
                </li>
            </ul>
        </div>
    </t>

    <!-- Progress Steps Template -->
    <t t-name="edu_base.ProgressStepsTemplate">
        <div class="edu_progress_steps">
            <div class="edu_step" t-foreach="props.steps" t-as="step" t-key="step_index">
                <div class="edu_step_icon">
                    <t t-esc="step.icon"/>
                </div>
                <div class="edu_step_text">
                    <t t-esc="step.text"/>
                </div>
            </div>
        </div>
    </t>

    <!-- Action Buttons Template -->
    <t t-name="edu_base.ActionButtonsTemplate">
        <div class="edu_help_actions">
            <button t-foreach="props.actions" t-as="action" t-key="action_index"
                    t-attf-class="edu_action_btn {{action.class || 'btn-primary'}}"
                    t-on-click="action.handler"
                    t-att-aria-label="action.label">
                <i t-attf-class="fa fa-{{action.icon}}"></i>
                <t t-esc="action.text"/>
            </button>
        </div>
    </t>

    <!-- Quote Template -->
    <t t-name="edu_base.QuoteTemplate">
        <div class="edu_help_quote">
            <em><t t-esc="props.quote"/></em>
            <div class="text-right mt-2" t-if="props.author">
                <small>- <t t-esc="props.author"/></small>
            </div>
        </div>
    </t>
</templates>
