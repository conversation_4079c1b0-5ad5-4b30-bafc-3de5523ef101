/** @odoo-module **/

/**
 * Test suite for Enhanced Help Sections
 * Validates functionality and styling of help components
 */

export class EduHelpTestSuite {
    constructor() {
        this.testResults = [];
        this.passedTests = 0;
        this.failedTests = 0;
    }

    /**
     * Run all tests
     */
    async runAllTests() {
        console.log('🧪 Starting EDU Help Section Tests...');
        
        // CSS Tests
        await this.testCSSLoading();
        await this.testResponsiveDesign();
        await this.testAnimations();
        
        // JavaScript Tests
        await this.testInteractivity();
        await this.testAccessibility();
        
        // Content Tests
        await this.testContentStructure();
        
        this.displayResults();
    }

    /**
     * Test CSS loading and styling
     */
    async testCSSLoading() {
        const testName = 'CSS Loading and Styling';
        try {
            // Check if CSS classes are available
            const testElement = document.createElement('div');
            testElement.className = 'o_view_nocontent_edu';
            document.body.appendChild(testElement);
            
            const computedStyle = window.getComputedStyle(testElement);
            const hasCustomStyling = computedStyle.padding !== '0px' || 
                                   computedStyle.borderRadius !== '0px';
            
            document.body.removeChild(testElement);
            
            if (hasCustomStyling) {
                this.addTestResult(testName, true, 'CSS classes loaded successfully');
            } else {
                this.addTestResult(testName, false, 'CSS classes not properly loaded');
            }
        } catch (error) {
            this.addTestResult(testName, false, `Error: ${error.message}`);
        }
    }

    /**
     * Test responsive design
     */
    async testResponsiveDesign() {
        const testName = 'Responsive Design';
        try {
            // Test different viewport sizes
            const originalWidth = window.innerWidth;
            
            // Simulate mobile viewport
            Object.defineProperty(window, 'innerWidth', {
                writable: true,
                configurable: true,
                value: 375
            });
            
            // Check if mobile styles are applied
            const mobileTest = this.checkMobileStyles();
            
            // Restore original width
            Object.defineProperty(window, 'innerWidth', {
                writable: true,
                configurable: true,
                value: originalWidth
            });
            
            this.addTestResult(testName, mobileTest, 
                mobileTest ? 'Responsive design working' : 'Responsive design issues detected');
        } catch (error) {
            this.addTestResult(testName, false, `Error: ${error.message}`);
        }
    }

    /**
     * Test animations
     */
    async testAnimations() {
        const testName = 'CSS Animations';
        try {
            const testElement = document.createElement('div');
            testElement.className = 'edu_fade_in';
            document.body.appendChild(testElement);
            
            // Check if animation classes exist
            const hasAnimation = testElement.classList.contains('edu_fade_in');
            
            document.body.removeChild(testElement);
            
            this.addTestResult(testName, hasAnimation, 
                hasAnimation ? 'Animation classes working' : 'Animation classes not found');
        } catch (error) {
            this.addTestResult(testName, false, `Error: ${error.message}`);
        }
    }

    /**
     * Test interactive functionality
     */
    async testInteractivity() {
        const testName = 'Interactive Functionality';
        try {
            // Check if global functions are available
            const functionsExist = typeof window.eduHelpFunctions === 'object' &&
                                 typeof window.eduHelpFunctions.createNewRecord === 'function' &&
                                 typeof window.eduHelpFunctions.copyTips === 'function';
            
            this.addTestResult(testName, functionsExist, 
                functionsExist ? 'Interactive functions available' : 'Interactive functions missing');
        } catch (error) {
            this.addTestResult(testName, false, `Error: ${error.message}`);
        }
    }

    /**
     * Test accessibility features
     */
    async testAccessibility() {
        const testName = 'Accessibility Features';
        try {
            // Create test help section
            const helpSection = this.createTestHelpSection();
            document.body.appendChild(helpSection);
            
            // Check ARIA attributes
            const hasAriaLabel = helpSection.hasAttribute('aria-label');
            const hasRole = helpSection.hasAttribute('role');
            const buttonsHaveTabindex = Array.from(helpSection.querySelectorAll('button'))
                .every(btn => btn.hasAttribute('tabindex') || btn.tabIndex >= 0);
            
            document.body.removeChild(helpSection);
            
            const accessibilityScore = [hasAriaLabel, hasRole, buttonsHaveTabindex]
                .filter(Boolean).length;
            
            this.addTestResult(testName, accessibilityScore >= 2, 
                `Accessibility score: ${accessibilityScore}/3`);
        } catch (error) {
            this.addTestResult(testName, false, `Error: ${error.message}`);
        }
    }

    /**
     * Test content structure
     */
    async testContentStructure() {
        const testName = 'Content Structure';
        try {
            const helpSection = this.createTestHelpSection();
            document.body.appendChild(helpSection);
            
            // Check required elements
            const hasTitle = helpSection.querySelector('.edu_help_title') !== null;
            const hasSubtitle = helpSection.querySelector('.edu_help_subtitle') !== null;
            const hasActions = helpSection.querySelector('.edu_help_actions') !== null;
            const hasFeatures = helpSection.querySelector('.edu_alert_enhanced') !== null;
            
            document.body.removeChild(helpSection);
            
            const structureScore = [hasTitle, hasSubtitle, hasActions, hasFeatures]
                .filter(Boolean).length;
            
            this.addTestResult(testName, structureScore >= 3, 
                `Structure score: ${structureScore}/4`);
        } catch (error) {
            this.addTestResult(testName, false, `Error: ${error.message}`);
        }
    }

    /**
     * Helper method to check mobile styles
     */
    checkMobileStyles() {
        // This is a simplified check - in a real scenario, 
        // you'd check specific CSS media query behaviors
        return window.innerWidth <= 768;
    }

    /**
     * Create a test help section element
     */
    createTestHelpSection() {
        const helpSection = document.createElement('div');
        helpSection.className = 'o_view_nocontent_edu';
        helpSection.setAttribute('role', 'region');
        helpSection.setAttribute('aria-label', 'Help section');
        
        helpSection.innerHTML = `
            <div class="edu_help_welcome">
                <h1 class="edu_help_title">Test Title</h1>
                <p class="edu_help_subtitle">Test Subtitle</p>
            </div>
            <div class="edu_help_actions">
                <button class="edu_action_btn btn-primary" tabindex="0">Test Button</button>
            </div>
            <div class="alert edu_alert_enhanced alert-info">
                <h5>Test Features</h5>
                <ul><li>Test Feature 1</li></ul>
            </div>
        `;
        
        return helpSection;
    }

    /**
     * Add test result
     */
    addTestResult(testName, passed, message) {
        this.testResults.push({ testName, passed, message });
        if (passed) {
            this.passedTests++;
            console.log(`✅ ${testName}: ${message}`);
        } else {
            this.failedTests++;
            console.log(`❌ ${testName}: ${message}`);
        }
    }

    /**
     * Display test results
     */
    displayResults() {
        const totalTests = this.passedTests + this.failedTests;
        const successRate = ((this.passedTests / totalTests) * 100).toFixed(1);
        
        console.log('\n📊 Test Results Summary:');
        console.log(`Total Tests: ${totalTests}`);
        console.log(`Passed: ${this.passedTests}`);
        console.log(`Failed: ${this.failedTests}`);
        console.log(`Success Rate: ${successRate}%`);
        
        if (this.failedTests === 0) {
            console.log('🎉 All tests passed! Help sections are working correctly.');
        } else {
            console.log('⚠️ Some tests failed. Please review the issues above.');
        }
        
        return {
            totalTests,
            passedTests: this.passedTests,
            failedTests: this.failedTests,
            successRate: parseFloat(successRate),
            results: this.testResults
        };
    }
}

// Auto-run tests in development mode
if (window.location.hostname === 'localhost' || window.location.hostname.includes('dev')) {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => {
            const testSuite = new EduHelpTestSuite();
            testSuite.runAllTests();
        }, 2000); // Wait for other scripts to load
    });
}

// Export for manual testing
window.EduHelpTestSuite = EduHelpTestSuite;
