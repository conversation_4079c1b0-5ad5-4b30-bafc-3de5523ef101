{
    'name': 'Midlogic Education Base',
    'version': '********.0',
    'category': 'Education',
    'summary': 'Base module for Midlogic Education Management System',
    'description': """
        This module provides the foundation for the Midlogic Education Management System.
        It includes core models and functionalities for managing educational institutions,
        students, courses, and related operations.
    """,
    'author': 'Midlogic',
    'website': 'https://www.midlogic.com',
    'license': 'LGPL-3',
    'depends': ['base', 'mail','common_odoo','contacts','common_mixins','documents'],
    'data': [
        'security/edu_security.xml',
        'security/ir.model.access.csv',
        'data/mid_edu_sequence.xml',
        'data/mid_edu_category.xml',
        'data/edu_relationship_data.xml',
        'data/mother_tongue_data.xml',
        'data/religion_data.xml',
        'data/email_templates.xml',
        'data/edu_config_data.xml',
        'views/res_config_settings_views.xml',
        'views/student_views.xml',
        'views/academic_views.xml',
        'views/term_views.xml',
        'views/course_views.xml',
        'views/division_views.xml',
        'views/category_views.xml',
        'views/program_views.xml',
        'views/relationship_view.xml',
        'views/batch_views.xml',
        'views/section_views.xml',
        'views/standard_views.xml',
        'views/faculty_views.xml',
        'views/department_views.xml',
        'views/subject_views.xml',
        'views/enrollment_views.xml',
        'views/mother_tongue_views.xml',
        'views/religion_views.xml',
        'views/menu.xml',
        'wizard/edu_security_wizard_views.xml',
        'wizard/student_management_wizard_views.xml',
'reports/report_templates.xml',
    ],
    'demo': [
        'demo/demo_academic_years.xml',
        'demo/demo_students.xml',
        'demo/demo_faculty.xml',
        'demo/demo_relations.xml',
    ],
    'assets': {
        'web.assets_backend': [
            'edu_base/static/src/css/help_sections.css',
            'edu_base/static/src/js/help_framework.js',
            'edu_base/static/src/js/help_sections.js',
            'edu_base/static/src/xml/help_templates.xml',
        ],
        'web.assets_backend_dev': [
            'edu_base/static/src/js/help_test.js',
        ],
    },
    'installable': True,
    'application': True,
    'auto_install': False,
    'icon': 'custom/edu/edu_base/static/description/icon.svg',
}
