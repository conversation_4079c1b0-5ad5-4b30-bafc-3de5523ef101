/** @odoo-module **/

/**
 * Enhanced Help Section Framework for EDU Base Module
 * Provides standardized help content and configurations for all models
 */

export const EduHelpConfig = {
    // Common alert types and their semantic meanings
    ALERT_TYPES: {
        PRIMARY: 'primary',    // Core functionality, main features
        SUCCESS: 'success',    // Achievements, positive outcomes
        INFO: 'info',         // Information, guidance, tips
        WARNING: 'warning',   // Important notes, cautions
        SECONDARY: 'secondary' // Additional features, optional items
    },

    // Common emoji sets for different contexts
    EMOJIS: {
        ACADEMIC: ['📚', '🎓', '📖', '✏️', '📝', '🏫'],
        PEOPLE: ['👨‍🏫', '👩‍🎓', '👥', '👤', '👨‍👩‍👧‍👦'],
        MANAGEMENT: ['📊', '📋', '🗂️', '📁', '🏛️', '⚙️'],
        SUCCESS: ['🏆', '🌟', '✨', '🎯', '💫', '🚀'],
        TIME: ['📅', '⏰', '📆', '⏱️', '🕐'],
        LOCATION: ['🏫', '🏛️', '🏢', '🌍', '📍']
    },

    // Standard quick start steps for different model types
    QUICK_STEPS: {
        PERSON_MANAGEMENT: [
            { icon: "👤", text: "Add Profile", description: "Create a new profile with basic information" },
            { icon: "📋", text: "Fill Details", description: "Complete all required and optional fields" },
            { icon: "🔗", text: "Link Relations", description: "Connect with related records and dependencies" },
            { icon: "📊", text: "Track Progress", description: "Monitor and manage ongoing activities" }
        ],
        ACADEMIC_SETUP: [
            { icon: "📚", text: "Create Structure", description: "Set up the basic academic framework" },
            { icon: "⚙️", text: "Configure Rules", description: "Define policies and business rules" },
            { icon: "👥", text: "Assign Resources", description: "Allocate faculty, rooms, and materials" },
            { icon: "🚀", text: "Activate", description: "Launch and begin operations" }
        ],
        ADMINISTRATIVE: [
            { icon: "📋", text: "Define Categories", description: "Create organizational categories" },
            { icon: "🏗️", text: "Build Hierarchy", description: "Establish relationships and structure" },
            { icon: "⚡", text: "Set Permissions", description: "Configure access and security" },
            { icon: "📈", text: "Monitor Usage", description: "Track adoption and performance" }
        ]
    }
};

/**
 * Help content configurations for each model
 */
export const EduHelpContent = {
    'edu.student': {
        title: "🎓 Student Management",
        subtitle: "Every great journey begins with a single step!",
        tagline: "Building Tomorrow's Leaders",
        description: "Start building your educational community by adding your first student. This is where dreams take shape and futures are molded.",
        alertType: EduHelpConfig.ALERT_TYPES.INFO,
        recordType: "Student",
        features: [
            "📝 Complete student profiles with photos and personal details",
            "🎯 Academic enrollment, grades, and performance tracking",
            "🏥 Health records, allergies, and special accommodation needs",
            "👨‍👩‍👧‍👦 Guardian contacts and emergency information",
            "🌍 International student documentation and visa status",
            "🏆 Achievements, awards, and extracurricular activities"
        ],
        quote: "Education is the most powerful weapon which you can use to change the world. - Nelson Mandela",
        quickSteps: EduHelpConfig.QUICK_STEPS.PERSON_MANAGEMENT,
        documentationUrl: "/documentation/student-management"
    },

    'edu.faculty': {
        title: "👨‍🏫 Faculty Management",
        subtitle: "Great teachers inspire great minds!",
        tagline: "Empowering Educators",
        description: "Build your dream team of educators who will shape the next generation. Every faculty member is a cornerstone of academic excellence.",
        alertType: EduHelpConfig.ALERT_TYPES.SUCCESS,
        recordType: "Faculty",
        features: [
            "👤 Comprehensive faculty profiles and qualifications",
            "📚 Teaching assignments, subjects, and class schedules",
            "🎓 Academic credentials, certifications, and research work",
            "⚡ Administrative roles, committees, and leadership positions",
            "📊 Performance reviews, feedback, and career development",
            "🏆 Awards, recognitions, and professional achievements"
        ],
        quote: "A good teacher can inspire hope, ignite the imagination, and instill a love of learning. - Brad Henry",
        quickSteps: EduHelpConfig.QUICK_STEPS.PERSON_MANAGEMENT,
        documentationUrl: "/documentation/faculty-management"
    },

    'edu.academic.year': {
        title: "📅 Academic Year Management",
        subtitle: "Time to structure your academic journey!",
        tagline: "Organizing Academic Excellence",
        description: "Academic years are the foundation of your educational calendar. Set up your first year to begin organizing terms, semesters, and academic milestones.",
        alertType: EduHelpConfig.ALERT_TYPES.PRIMARY,
        recordType: "Academic Year",
        features: [
            "🗓️ Academic calendar setup and milestone planning",
            "📊 Term and semester organization",
            "🎯 Academic goal setting and tracking",
            "📈 Performance analytics and reporting",
            "🔄 Year-end processes and transitions",
            "📋 Compliance and accreditation management"
        ],
        quote: "The future belongs to those who prepare for it today. - Malcolm X",
        quickSteps: EduHelpConfig.QUICK_STEPS.ACADEMIC_SETUP,
        documentationUrl: "/documentation/academic-year"
    },

    'edu.course': {
        title: "📚 Course Management",
        subtitle: "Knowledge begins with a single course!",
        tagline: "Designing Learning Experiences",
        description: "Design the building blocks of education by creating comprehensive courses that will inspire and educate students across different standards and levels.",
        alertType: EduHelpConfig.ALERT_TYPES.INFO,
        recordType: "Course",
        features: [
            "📝 Detailed course descriptions and learning objectives",
            "🎯 Curriculum mapping and competency frameworks",
            "📊 Assessment strategies and evaluation methods",
            "🔗 Prerequisites and course sequencing",
            "📚 Resource allocation and material management",
            "👥 Faculty assignment and teaching coordination"
        ],
        quote: "Education is not preparation for life; education is life itself. - John Dewey",
        quickSteps: EduHelpConfig.QUICK_STEPS.ACADEMIC_SETUP,
        documentationUrl: "/documentation/course-management"
    },

    'edu.department': {
        title: "🏛️ Department Management",
        subtitle: "Building academic excellence, one department at a time!",
        tagline: "Organizing Academic Excellence",
        description: "Create the organizational backbone of your institution. Departments are where knowledge domains come alive and academic communities thrive.",
        alertType: EduHelpConfig.ALERT_TYPES.WARNING,
        recordType: "Department",
        features: [
            "🏗️ Department structure, hierarchy, and leadership roles",
            "👥 Faculty assignments, workload distribution, and team management",
            "📖 Academic programs, curriculum design, and subject coordination",
            "💰 Budget planning, allocation, and financial oversight",
            "🏅 Accreditation status, compliance, and quality assurance",
            "🔬 Research initiatives, labs, and infrastructure management"
        ],
        quote: "Excellence is never an accident. It is always the result of high intention, sincere effort, and intelligent execution. - Aristotle",
        quickSteps: EduHelpConfig.QUICK_STEPS.ADMINISTRATIVE,
        documentationUrl: "/documentation/department-management"
    },

    'edu.category': {
        title: "🏷️ Category Management",
        subtitle: "Organization starts with smart categorization!",
        tagline: "Structuring Information",
        description: "Create meaningful categories that help organize and classify your educational content, making everything easier to find and manage.",
        alertType: EduHelpConfig.ALERT_TYPES.INFO,
        recordType: "Category",
        features: [
            "📚 Subject areas and academic disciplines",
            "🎯 Learning objectives and skill categories",
            "📊 Assessment types and evaluation methods",
            "🏆 Achievement levels and performance tiers",
            "🔍 Search filters and content classification",
            "📋 Administrative groupings and workflows"
        ],
        quote: "For every minute spent organizing, an hour is earned. - Benjamin Franklin",
        quickSteps: EduHelpConfig.QUICK_STEPS.ADMINISTRATIVE,
        documentationUrl: "/documentation/category-management"
    }
};

/**
 * Helper function to get help configuration for a model
 */
export function getHelpConfig(modelName) {
    return EduHelpContent[modelName] || {
        title: "📋 Management System",
        subtitle: "Welcome to the management interface!",
        tagline: "Efficient Management",
        description: "Manage your records efficiently with our comprehensive tools and features.",
        alertType: EduHelpConfig.ALERT_TYPES.PRIMARY,
        recordType: "Record",
        features: [
            "📝 Create and manage records",
            "🔍 Search and filter capabilities",
            "📊 Reporting and analytics",
            "⚙️ Configuration and settings"
        ],
        quote: "Success is where preparation and opportunity meet. - Bobby Unser",
        quickSteps: EduHelpConfig.QUICK_STEPS.ADMINISTRATIVE
    };
}

/**
 * Generate help HTML for a given model
 */
export function generateHelpHTML(modelName, customConfig = {}) {
    const config = { ...getHelpConfig(modelName), ...customConfig };
    
    return `
        <div class="o_view_nocontent_edu edu_fade_in">
            <div class="edu_help_welcome">
                <h1 class="edu_help_title">${config.title}</h1>
                <p class="edu_help_subtitle">${config.subtitle}</p>
                <p class="edu_help_tagline">
                    <strong>${config.tagline}</strong><br/>
                    ${config.description}
                </p>
            </div>
            
            <div class="edu_help_actions">
                <button class="edu_action_btn btn-primary" onclick="this.createNewRecord()">
                    <i class="fa fa-plus"></i> Create New ${config.recordType}
                </button>
                <button class="edu_action_btn btn-success" onclick="this.showQuickStart()">
                    <i class="fa fa-rocket"></i> Quick Start Guide
                </button>
                <button class="edu_action_btn btn-outline-secondary" onclick="this.openDocumentation()">
                    <i class="fa fa-book"></i> Documentation
                </button>
            </div>
            
            <div class="alert edu_alert_enhanced alert-${config.alertType}">
                <h5><i class="fa fa-bullseye"></i> What you can manage here:</h5>
                <ul class="mb-0">
                    ${config.features.map(feature => `<li>${feature}</li>`).join('')}
                </ul>
            </div>
            
            ${config.quote ? `
                <div class="edu_help_quote">
                    <em>${config.quote}</em>
                </div>
            ` : ''}
        </div>
    `;
}
