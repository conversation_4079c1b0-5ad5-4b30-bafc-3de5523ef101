Metadata-Version: 2.4
Name: barcode
Version: 1.0.4
Summary: Design and test NGS barcodes.
Home-page: https://github.com/jfjlaros/barcode
Author: <PERSON><PERSON><PERSON> <PERSON><PERSON>
Author-email: J<PERSON><PERSON><PERSON><PERSON>@lumc.nl
License: MIT
Keywords: bioinformatics
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Science/Research
Classifier: Intended Audience :: Developers
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Topic :: Scientific/Engineering
License-File: LICENSE
Requires-Dist: python-Levenshtein>=0.12.0
Dynamic: license-file

Barcode: Design and validate NGS barcodes
=========================================

.. image:: https://img.shields.io/github/last-commit/jfjlaros/barcode.svg
   :target: https://github.com/jfjlaros/barcode/graphs/commit-activity
.. image:: https://github.com/jfjlaros/barcode/actions/workflows/python-package.yml/badge.svg
   :target: https://github.com/jfjlaros/barcode/actions/workflows/python-package.yml
.. image:: https://readthedocs.org/projects/barcode/badge/?version=latest
   :target: https://barcode.readthedocs.io/en/latest
.. image:: https://img.shields.io/github/release-date/jfjlaros/barcode.svg
   :target: https://github.com/jfjlaros/barcode/releases
.. image:: https://img.shields.io/github/release/jfjlaros/barcode.svg
   :target: https://github.com/jfjlaros/barcode/releases
.. image:: https://img.shields.io/pypi/v/barcode.svg
   :target: https://pypi.org/project/barcode/
.. image:: https://img.shields.io/github/languages/code-size/jfjlaros/barcode.svg
   :target: https://github.com/jfjlaros/barcode
.. image:: https://img.shields.io/github/languages/count/jfjlaros/barcode.svg
   :target: https://github.com/jfjlaros/barcode
.. image:: https://img.shields.io/github/languages/top/jfjlaros/barcode.svg
   :target: https://github.com/jfjlaros/barcode
.. image:: https://img.shields.io/github/license/jfjlaros/barcode.svg
   :target: https://raw.githubusercontent.com/jfjlaros/barcode/master/LICENSE.md

----

Barcode is a program for the design and validation of sets of sequencing
barcodes.

Please see ReadTheDocs_ for the latest documentation.


.. _ReadTheDocs: https://barcode.readthedocs.io/en/latest/index.html
