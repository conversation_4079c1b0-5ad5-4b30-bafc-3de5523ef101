<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <!-- List View -->
    <record id="view_edu_academic_year_list" model="ir.ui.view">
        <field name="name">edu.academic.year.list</field>
        <field name="model">edu.academic.year</field>
        <field name="arch" type="xml">
            <list string="Academic Years">
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="code"/>
                <field name="start_date"/>
                <field name="end_date"/>
                <field name="state" decoration-success="state == 'active'" decoration-danger="state == 'closed'" decoration-info="state == 'draft'"/>
                <field name="term_count"/>
                <field name="responsible_id" optional="show"/>
                <field name="company_id" optional="hide" groups="base.group_multi_company"/>
            </list>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_edu_academic_year_form" model="ir.ui.view">
        <field name="name">edu.academic.year.form</field>
        <field name="model">edu.academic.year</field>
        <field name="arch" type="xml">
            <form string="Academic Year">
                <header>
                    <field name="state" widget="statusbar"/>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="code"/>
                            <field name="sequence"/>
                        </group>
                        <group>
                            <field name="start_date"/>
                            <field name="end_date"/>
                            <field name="responsible_id"/>
                            <field name="company_id" groups="base.group_multi_company"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Description" name="description">
                            <field name="description" placeholder="Add a description..."/>
                        </page>
                        <page string="Terms" name="terms">
                            <field name="term_ids"/>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- Search View -->
    <record id="view_edu_academic_year_search" model="ir.ui.view">
        <field name="name">edu.academic.year.search</field>
        <field name="model">edu.academic.year</field>
        <field name="arch" type="xml">
            <search string="Academic Years">
                <field name="name"/>
                <field name="code"/>
                <field name="responsible_id"/>
                <separator/>
                <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
                <filter string="Active" name="active" domain="[('state', '=', 'active')]"/>
                <filter string="Closed" name="closed" domain="[('state', '=', 'closed')]"/>
                <group expand="0" string="Group By">
                    <filter string="Status" name="group_by_state" context="{'group_by': 'state'}"/>
                    <filter string="Coordinator" name="group_by_responsible" context="{'group_by': 'responsible_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action -->
    <record id="action_edu_academic_year" model="ir.actions.act_window">
        <field name="name">Academic Years</field>
        <field name="res_model">edu.academic.year</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="view_edu_academic_year_search"/>
        <field name="help" type="html">
            <div class="o_view_nocontent_edu edu_fade_in">
                <!-- Welcome Section -->
                <div class="edu_help_welcome">
                    <h1 class="edu_help_title">📅 Academic Year Management</h1>
                    <p class="edu_help_subtitle">Time to structure your academic journey!</p>
                    <p class="edu_help_tagline">
                        <strong>Organizing Academic Excellence</strong><br/>
                        Academic years are the foundation of your educational calendar. Set up your first year to begin organizing terms, semesters, and academic milestones.
                    </p>
                </div>

                <!-- Quick Start Steps -->
                <div class="edu_progress_steps">
                    <div class="edu_step">
                        <div class="edu_step_icon">📅</div>
                        <div class="edu_step_text">Create Year</div>
                    </div>
                    <div class="edu_step">
                        <div class="edu_step_icon">⚙️</div>
                        <div class="edu_step_text">Set Dates</div>
                    </div>
                    <div class="edu_step">
                        <div class="edu_step_icon">📊</div>
                        <div class="edu_step_text">Add Terms</div>
                    </div>
                    <div class="edu_step">
                        <div class="edu_step_icon">🚀</div>
                        <div class="edu_step_text">Activate</div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="edu_help_actions">
                    <a href="#" class="edu_action_btn btn-primary" onclick="this.createNewRecord()">
                        <i class="fa fa-plus"></i>
                        Create Academic Year
                    </a>
                    <a href="#" class="edu_action_btn btn-success" onclick="this.showQuickStart()">
                        <i class="fa fa-rocket"></i>
                        Quick Start Guide
                    </a>
                    <a href="/documentation/academic-year" class="edu_action_btn btn-outline-secondary" target="_blank">
                        <i class="fa fa-book"></i>
                        Documentation
                    </a>
                </div>

                <!-- Enhanced Features Alert -->
                <div class="alert edu_alert_enhanced alert-primary edu_slide_up">
                    <h5>
                        <i class="fa fa-bullseye"></i>
                        🗓️ What you can organize:
                    </h5>
                    <ul class="mb-0">
                        <li>📊 Academic year periods with start and end dates</li>
                        <li>🎯 Term divisions (semesters, quarters, trimesters)</li>
                        <li>📈 Academic progression and grade level transitions</li>
                        <li>🏫 Institution-wide calendar coordination</li>
                        <li>📋 Enrollment periods and academic deadlines</li>
                        <li>🔄 Year-end processes and transitions</li>
                        <li>📈 Performance analytics and reporting</li>
                    </ul>
                    <div class="mt-3">
                        <button class="btn btn-sm btn-outline-primary" onclick="this.copyTips()">
                            <i class="fa fa-copy"></i> Copy Tips
                        </button>
                        <button class="btn btn-sm btn-outline-info ml-2" onclick="this.showExamples()">
                            <i class="fa fa-lightbulb-o"></i> Examples
                        </button>
                    </div>
                </div>

                <!-- Inspirational Quote -->
                <div class="edu_help_quote">
                    <em>"The beautiful thing about learning is that no one can take it away from you."</em>
                    <div class="text-right mt-2">
                        <small>- B.B. King</small>
                    </div>
                </div>
            </div>
        </field>
    </record>
</odoo>