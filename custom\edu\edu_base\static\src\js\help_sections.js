/** @odoo-module **/

import { Component, onMounted, useState } from "@odoo/owl";
import { useService } from "@web/core/utils/hooks";

/**
 * Enhanced Help Section Component for EDU Base Module
 * Provides interactive help sections with animations and user guidance
 */
export class EduHelpSection extends Component {
    static template = "edu_base.HelpSectionTemplate";
    
    setup() {
        this.actionService = useService("action");
        this.notificationService = useService("notification");
        
        this.state = useState({
            currentStep: 0,
            isAnimating: false,
            showQuickStart: false
        });
        
        onMounted(() => {
            this.initializeAnimations();
            this.setupAccessibility();
        });
    }
    
    /**
     * Initialize entrance animations for help elements
     */
    initializeAnimations() {
        const elements = this.el.querySelectorAll('.edu_fade_in, .edu_slide_up');
        elements.forEach((el, index) => {
            setTimeout(() => {
                el.style.opacity = '1';
                el.style.transform = 'translateY(0)';
            }, index * 150);
        });
    }
    
    /**
     * Setup accessibility features
     */
    setupAccessibility() {
        // Add ARIA labels and roles
        const helpSection = this.el.querySelector('.o_view_nocontent_edu');
        if (helpSection) {
            helpSection.setAttribute('role', 'region');
            helpSection.setAttribute('aria-label', 'Help and guidance section');
        }
        
        // Setup keyboard navigation for action buttons
        const actionButtons = this.el.querySelectorAll('.edu_action_btn');
        actionButtons.forEach((btn, index) => {
            btn.setAttribute('tabindex', '0');
            btn.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    btn.click();
                }
            });
        });
    }
    
    /**
     * Handle quick start guide
     */
    toggleQuickStart() {
        this.state.showQuickStart = !this.state.showQuickStart;
        
        if (this.state.showQuickStart) {
            this.startQuickGuide();
        }
    }
    
    /**
     * Start interactive quick guide
     */
    startQuickGuide() {
        this.state.currentStep = 0;
        this.state.isAnimating = true;
        
        const steps = this.el.querySelectorAll('.edu_step');
        steps.forEach((step, index) => {
            setTimeout(() => {
                step.classList.add('active');
                if (index === steps.length - 1) {
                    this.state.isAnimating = false;
                }
            }, index * 800);
        });
    }
    
    /**
     * Navigate to create new record
     */
    createNewRecord() {
        const modelName = this.props.modelName;
        if (modelName) {
            this.actionService.doAction({
                type: 'ir.actions.act_window',
                res_model: modelName,
                view_mode: 'form',
                views: [[false, 'form']],
                target: 'current',
                context: this.props.context || {}
            });
        }
    }
    
    /**
     * Open documentation or help resources
     */
    openDocumentation() {
        const docUrl = this.props.documentationUrl || '/web/static/src/legacy/js/views/basic_view.js';
        window.open(docUrl, '_blank', 'noopener,noreferrer');
    }
    
    /**
     * Show demo data or examples
     */
    showExamples() {
        this.notificationService.add(
            "Demo data and examples will be available in the next update!",
            { type: "info", title: "Coming Soon" }
        );
    }
    
    /**
     * Handle feature highlight
     */
    highlightFeature(featureIndex) {
        const features = this.el.querySelectorAll('.edu_alert_enhanced li');
        features.forEach((feature, index) => {
            if (index === featureIndex) {
                feature.style.backgroundColor = 'rgba(0, 123, 255, 0.1)';
                feature.style.transform = 'scale(1.02)';
                setTimeout(() => {
                    feature.style.backgroundColor = '';
                    feature.style.transform = '';
                }, 2000);
            }
        });
    }
    
    /**
     * Copy helpful tips to clipboard
     */
    copyTips() {
        const tips = this.el.querySelectorAll('.edu_alert_enhanced li');
        let tipsText = "Quick Tips:\n";
        tips.forEach((tip, index) => {
            tipsText += `${index + 1}. ${tip.textContent.trim()}\n`;
        });
        
        navigator.clipboard.writeText(tipsText).then(() => {
            this.notificationService.add(
                "Tips copied to clipboard!",
                { type: "success" }
            );
        }).catch(() => {
            this.notificationService.add(
                "Failed to copy tips to clipboard",
                { type: "warning" }
            );
        });
    }
    
    /**
     * Handle responsive behavior
     */
    handleResize() {
        const isMobile = window.innerWidth < 768;
        const helpSection = this.el.querySelector('.o_view_nocontent_edu');
        
        if (helpSection) {
            if (isMobile) {
                helpSection.classList.add('mobile-view');
            } else {
                helpSection.classList.remove('mobile-view');
            }
        }
    }
}

/**
 * Help Section Template Registry
 */
export const EduHelpTemplates = {
    student: {
        title: "🎓 Student Management",
        subtitle: "Every great journey begins with a single step!",
        description: "Start building your educational community by adding your first student. This is where dreams take shape and futures are molded.",
        alertType: "info",
        features: [
            "📝 Complete student profiles with photos and personal details",
            "🎯 Academic enrollment, grades, and performance tracking", 
            "🏥 Health records, allergies, and special accommodation needs",
            "👨‍👩‍👧‍👦 Guardian contacts and emergency information",
            "🌍 International student documentation and visa status",
            "🏆 Achievements, awards, and extracurricular activities"
        ],
        quote: "Education is the most powerful weapon which you can use to change the world. - Nelson Mandela",
        quickSteps: [
            { icon: "👤", text: "Add Student" },
            { icon: "📋", text: "Fill Details" },
            { icon: "🎓", text: "Enroll" },
            { icon: "📊", text: "Track Progress" }
        ]
    },
    
    faculty: {
        title: "👨‍🏫 Faculty Management",
        subtitle: "Great teachers inspire great minds!",
        description: "Build your dream team of educators who will shape the next generation. Every faculty member is a cornerstone of academic excellence.",
        alertType: "success",
        features: [
            "👤 Comprehensive faculty profiles and qualifications",
            "📚 Teaching assignments, subjects, and class schedules",
            "🎓 Academic credentials, certifications, and research work",
            "⚡ Administrative roles, committees, and leadership positions",
            "📊 Performance reviews, feedback, and career development",
            "🏆 Awards, recognitions, and professional achievements"
        ],
        quote: "A good teacher can inspire hope, ignite the imagination, and instill a love of learning. - Brad Henry",
        quickSteps: [
            { icon: "👨‍🏫", text: "Add Faculty" },
            { icon: "📜", text: "Add Credentials" },
            { icon: "📚", text: "Assign Subjects" },
            { icon: "📈", text: "Track Performance" }
        ]
    }
};

// Register the component
import { registry } from "@web/core/registry";
registry.category("components").add("EduHelpSection", EduHelpSection);

/**
 * Global helper functions for help sections
 */
window.eduHelpFunctions = {
    /**
     * Create new record action
     */
    createNewRecord() {
        const actionService = odoo.__DEBUG__.services.action;
        const currentAction = actionService.currentController?.action;

        if (currentAction && currentAction.res_model) {
            actionService.doAction({
                type: 'ir.actions.act_window',
                res_model: currentAction.res_model,
                view_mode: 'form',
                views: [[false, 'form']],
                target: 'current',
                context: currentAction.context || {}
            });
        }
    },

    /**
     * Show quick start guide
     */
    showQuickStart() {
        const notificationService = odoo.__DEBUG__.services.notification;
        notificationService.add(
            "Quick Start Guide will be available soon!",
            { type: "info", title: "Coming Soon" }
        );
    },

    /**
     * Copy tips to clipboard
     */
    copyTips() {
        const helpSection = document.querySelector('.edu_alert_enhanced');
        if (helpSection) {
            const tips = helpSection.querySelectorAll('li');
            let tipsText = "Quick Tips:\n";
            tips.forEach((tip, index) => {
                tipsText += `${index + 1}. ${tip.textContent.trim()}\n`;
            });

            navigator.clipboard.writeText(tipsText).then(() => {
                const notificationService = odoo.__DEBUG__.services.notification;
                notificationService.add(
                    "Tips copied to clipboard!",
                    { type: "success" }
                );
            }).catch(() => {
                const notificationService = odoo.__DEBUG__.services.notification;
                notificationService.add(
                    "Failed to copy tips to clipboard",
                    { type: "warning" }
                );
            });
        }
    },

    /**
     * Show examples
     */
    showExamples() {
        const notificationService = odoo.__DEBUG__.services.notification;
        notificationService.add(
            "Examples and demo data will be available in the next update!",
            { type: "info", title: "Coming Soon" }
        );
    },

    /**
     * Import functionality
     */
    importStudents() {
        this.showImportDialog('Students');
    },

    importFaculty() {
        this.showImportDialog('Faculty');
    },

    importCourses() {
        this.showImportDialog('Courses');
    },

    showImportDialog(recordType) {
        const notificationService = odoo.__DEBUG__.services.notification;
        notificationService.add(
            `${recordType} import functionality will be available soon!`,
            { type: "info", title: "Coming Soon" }
        );
    }
};

/**
 * Initialize help section interactions when DOM is ready
 */
document.addEventListener('DOMContentLoaded', function() {
    // Add click handlers to action buttons
    document.addEventListener('click', function(e) {
        if (e.target.closest('.edu_action_btn')) {
            const btn = e.target.closest('.edu_action_btn');
            const onclick = btn.getAttribute('onclick');

            if (onclick && onclick.includes('createNewRecord')) {
                e.preventDefault();
                window.eduHelpFunctions.createNewRecord();
            } else if (onclick && onclick.includes('showQuickStart')) {
                e.preventDefault();
                window.eduHelpFunctions.showQuickStart();
            } else if (onclick && onclick.includes('copyTips')) {
                e.preventDefault();
                window.eduHelpFunctions.copyTips();
            } else if (onclick && onclick.includes('showExamples')) {
                e.preventDefault();
                window.eduHelpFunctions.showExamples();
            } else if (onclick && onclick.includes('import')) {
                e.preventDefault();
                if (onclick.includes('Students')) {
                    window.eduHelpFunctions.importStudents();
                } else if (onclick.includes('Faculty')) {
                    window.eduHelpFunctions.importFaculty();
                } else if (onclick.includes('Courses')) {
                    window.eduHelpFunctions.importCourses();
                }
            }
        }
    });

    // Add hover effects to feature list items
    document.addEventListener('mouseenter', function(e) {
        if (e.target.closest('.edu_alert_enhanced li')) {
            const li = e.target.closest('.edu_alert_enhanced li');
            li.style.backgroundColor = 'rgba(0, 123, 255, 0.05)';
            li.style.transform = 'translateX(5px)';
            li.style.transition = 'all 0.3s ease';
        }
    }, true);

    document.addEventListener('mouseleave', function(e) {
        if (e.target.closest('.edu_alert_enhanced li')) {
            const li = e.target.closest('.edu_alert_enhanced li');
            li.style.backgroundColor = '';
            li.style.transform = '';
        }
    }, true);

    // Initialize animations
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('edu_fade_in');
            }
        });
    });

    document.querySelectorAll('.edu_slide_up').forEach(el => {
        observer.observe(el);
    });
});
