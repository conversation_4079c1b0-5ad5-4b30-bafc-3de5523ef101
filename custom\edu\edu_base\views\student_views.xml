<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Enhanced List View -->
    <record id="view_student_list" model="ir.ui.view">
        <field name="name">edu.student.list</field>
        <field name="model">edu.student</field>
        <field name="arch" type="xml">
            <list string="Students" default_order="name" multi_edit="1">
                <field name="image_128" string="Photo" widget="image" options="{'size': [40, 40]}"/>
                <field name="student_id" string="Student ID"/>
                <field name="name" string="Full Name"/>
                <field name="first_name" string="First Name" optional="hide"/>
                <field name="last_name" string="Last Name" optional="hide"/>
                <field name="display_name" string="Display Name" optional="hide"/>
                <field name="age" string="Age"/>
                <field name="gender" string="Gender"/>
                <field name="current_academic_year_id" string="Academic Year" optional="show"/>
                <field name="current_program_id" string="Program" optional="show"/>
                <field name="current_standard_id" string="Standard/Class" optional="show"/>
                <field name="current_section_id" string="Section" optional="show"/>
                <field name="overall_gpa" string="GPA" optional="show"/>
                <field name="overall_attendance_percentage" string="Attendance %" optional="show"/>
                <field name="state" string="Status" widget="badge" 
                       decoration-success="state == 'active'"
                       decoration-info="state == 'enrolled'"
                       decoration-warning="state == 'suspended'"
                       decoration-danger="state == 'expelled'"
                       decoration-muted="state == 'inactive'"/>
                <field name="admission_date" string="Admission Date" optional="hide"/>
                <field name="is_international" string="International" widget="boolean_toggle" optional="hide"/>
                <field name="company_id" string="Institution" groups="base.group_multi_company" optional="hide"/>
                <field name="active" invisible="1"/>
            </list>
        </field>
    </record>

    <!-- Enhanced Form View -->
    <record id="view_student_form" model="ir.ui.view">
        <field name="name">edu.student.form</field>
        <field name="model">edu.student</field>
        <field name="arch" type="xml">
            <form string="Student">
                <header>
                    <button name="action_enroll" string="Enroll" type="object" 
                            class="oe_highlight" invisible="state != 'draft'" 
                            confirm="Are you sure you want to enroll this student?"/>
                    <button name="action_activate" string="Activate" type="object" 
                            class="oe_highlight" invisible="state not in ['draft', 'inactive']"/>
                    <button name="action_deactivate" string="Deactivate" type="object" 
                            invisible="state != 'active'"/>
                    <button name="action_graduate" string="Graduate" type="object" 
                            class="oe_highlight" invisible="state != 'active'" 
                            confirm="Are you sure you want to graduate this student?"/>
                    <button name="action_transfer" string="Transfer" type="object" 
                            invisible="state not in ['active', 'inactive']"/>
                    <button name="action_suspend" string="Suspend" type="object" 
                            invisible="state not in ['active', 'enrolled']" 
                            confirm="Are you sure you want to suspend this student?"/>
                    <button name="action_expel" string="Expel" type="object" 
                            invisible="state not in ['active', 'enrolled', 'suspended']" 
                            confirm="Are you sure you want to expel this student?"/>
                    <button name="action_make_alumni" string="Make Alumni" type="object" 
                            invisible="state != 'graduated'"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,enrolled,active,graduated"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_enrollments" type="object" class="oe_stat_button" icon="fa-graduation-cap">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_text">Enrollments</span>
                            </div>
                        </button>
                        <button name="action_view_relations" type="object" class="oe_stat_button" icon="fa-users">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_text">Relations</span>
                            </div>
                        </button>
                        <button name="action_create_user_account" type="object" class="oe_stat_button" 
                                icon="fa-user" invisible="user_id">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_text">Create User</span>
                            </div>
                        </button>
                        <button name="action_generate_student_card" type="object" class="oe_stat_button" icon="fa-id-card">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_text">Student Card</span>
                            </div>
                        </button>
                        <button name="action_view_documents" type="object" class="oe_stat_button" icon="fa-files-o">
                            <field name="document_count" widget="statinfo" string="Documents"/>
                        </button>
                    </div>
                    
                    <field name="image_1920" widget="image" class="oe_avatar" 
                           options="{'preview_image': 'image_128', 'size': [150, 150]}"/>
                    
                    <div class="oe_title">
                        <label for="name" class="oe_edit_only"/>
                        <h1>
                            <field name="name" placeholder="Student Full Name"/>
                        </h1>
                        <h3>
                            <field name="student_id" placeholder="Student ID" readonly="1"/>
                        </h3>
                    </div>

                    <group>
                        <group name="name_info" string="Name Information">
                            <field name="first_name" required="1"/>
                            <field name="middle_name"/>
                            <field name="last_name" required="1"/>
                            <field name="initial"/>
                            <field name="display_name" readonly="1"/>
                        </group>
                        <group name="basic_info" string="Basic Information">
                            <field name="admission_number"/>
                            <field name="admission_date"/>
                            <field name="birth_date" required="1"/>
                            <field name="age" readonly="1"/>
                            <field name="age_group" readonly="1"/>
                            <field name="gender" required="1"/>
                            <field name="nationality" required="1"/>
                            <field name="birth_place"/>
                            <field name="mother_tongue_id"/>
                            <field name="religion_id"/>
                            <field name="birth_certificate_no"/>
                        </group>
                        <group name="academic_info" string="Current Academic Information">
                            <field name="current_academic_year_id"/>
                            <field name="current_program_id"/>
                            <field name="current_standard_id"/>
                            <field name="current_section_id"/>
                            <field name="current_enrollment_id" readonly="1"/>
                            <field name="graduation_date"/>
                            <field name="days_since_admission" readonly="1"/>
                            <field name="is_birthday_today" readonly="1" widget="boolean_toggle"/>
                        </group>
                    </group>

                    <notebook>
                        <page string="Contact Information" name="contact_info">
                            <group>
                                <group string="Address" name="address">
                                    <field name="street" placeholder="Street..."/>
                                    <field name="street2" placeholder="Street 2..."/>
                                    <field name="city" placeholder="City"/>
                                    <field name="state_id" placeholder="State" options="{'no_create': True}"/>
                                    <field name="zip" placeholder="ZIP"/>
                                    <field name="country_id" placeholder="Country" options="{'no_create': True}"/>
                                </group>
                                <group string="Communication" name="communication">
                                    <field name="email" widget="email"/>
                                    <field name="phone" widget="phone"/>
                                    <field name="mobile" widget="phone"/>
                                    <field name="website" widget="url"/>
                                </group>
                            </group>
                        </page>

                        <!-- Medical Information moved to edu_medical addon -->

                        <page string="International Student" name="international_info" invisible="not is_international">
                            <group>
                                <group string="Visa Information" name="visa_info">
                                    <field name="visa_type"/>
                                    <field name="visa_number"/>
                                    <field name="visa_expiry_date"/>
                                    <field name="passport_number"/>
                                    <field name="passport_expiry_date"/>
                                    <field name="passport_issuing_country_id"/>
                                </group>
                                <group string="Language  &amp;  Cultural Proficiency" name="language_info">
                                    <field name="english_proficiency"/>
                                    <field name="other_languages"/>
                                    <field name="home_country_address"/>
                                </group>
                            </group>
                        </page>

                        <page string="Guardian  &amp;  Emergency Contacts" name="guardians">
                            <group>
                                <group string="Primary Guardian" name="primary_guardian">
                                    <field name="local_guardian_id"/>
                                </group>
                                <group string="Emergency Contacts" name="emergency_contacts">
                                    <field name="emergency_contact_ids" nolabel="1">
                                        <list string="Emergency Contacts" editable="bottom">
                                            <field name="contact_id"/>
                                            <field name="relationship_id"/>
                                            <field name="is_emergency"/>
                                            <field name="state"/>
                                        </list>
                                    </field>
                                </group>
                            </group>
                            <separator string="All Guardians"/>
                            <field name="guardian_ids" nolabel="1">
                                <list string="Guardians" editable="bottom">
                                    <field name="contact_id"/>
                                    <field name="relationship_id"/>
                                    <field name="is_guardian"/>
                                    <field name="is_emergency"/>
                                    <field name="enable_access"/>
                                    <field name="state"/>
                                </list>
                            </field>
                        </page>

                        <page string="Relations" name="relations">
                            <field name="relation_ids" nolabel="1">
                                <list string="Relations" editable="bottom">
                                    <field name="contact_id"/>
                                    <field name="relationship_id"/>
                                    <field name="is_emergency"/>
                                    <field name="is_guardian"/>
                                    <field name="state"/>
                                </list>
                            </field>
                        </page>

                        <page string="Academic Performance" name="performance">
                            <group>
                                <group string="Academic Metrics" name="academic_metrics">
                                    <field name="overall_gpa" readonly="1"/>
                                    <field name="overall_attendance_percentage" readonly="1"/>
                                    <field name="disciplinary_actions_count" readonly="1"/>
                                    <field name="achievements_count" readonly="1"/>
                                </group>
                                <group string="Transportation  &amp;  Scholarship" name="transport_scholarship">
                                    <field name="transportation_mode"/>
                                    <field name="scholarship_details"/>
                                </group>
                            </group>
                        </page>

                        <page string="Additional Information" name="additional_info">
                            <group>
                                <group string="System Information" name="system_info">
                                    <field name="user_id" readonly="1"/>
                                    <field name="company_id" groups="base.group_multi_company"/>
                                    <field name="active"/>
                                </group>
                            </group>
                            <separator string="Notes"/>
                            <field name="notes" nolabel="1" placeholder="Additional notes about the student..."/>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- Search View -->
    <record id="view_student_search" model="ir.ui.view">
        <field name="name">edu.student.search</field>
        <field name="model">edu.student</field>
        <field name="arch" type="xml">
            <search string="Students">
                <field name="name" string="Student Name" filter_domain="['|', '|', '|', '|', ('name', 'ilike', self), ('first_name', 'ilike', self), ('last_name', 'ilike', self), ('student_id', 'ilike', self), ('rg_no', 'ilike', self)]"/>
                <field name="first_name"/>
                <field name="last_name"/>
                <field name="student_id"/>
                <field name="rg_no"/>
                <field name="email"/>
                <field name="phone"/>
                <field name="mobile"/>
                <field name="current_academic_year_id"/>
                <field name="current_program_id"/>
                <field name="current_standard_id"/>
                <field name="current_section_id"/>
                
                <separator/>
                <filter string="Active Students" name="active_students" domain="[('state', '=', 'active')]"/>
                <filter string="Enrolled Students" name="enrolled_students" domain="[('state', '=', 'enrolled')]"/>
                <filter string="Graduated Students" name="graduated_students" domain="[('state', '=', 'graduated')]"/>
                <filter string="International Students" name="international_students" domain="[('is_international', '=', True)]"/>
                <filter string="Birthday Today" name="birthday_today" domain="[('is_birthday_today', '=', True)]"/>
                
                <separator/>
                <filter string="Male" name="male" domain="[('gender', '=', 'male')]"/>
                <filter string="Female" name="female" domain="[('gender', '=', 'female')]"/>
                
                <separator/>
                <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                
                <group expand="0" string="Group By">
                    <filter string="Status" name="group_by_state" context="{'group_by': 'state'}"/>
                    <filter string="Gender" name="group_by_gender" context="{'group_by': 'gender'}"/>
                    <filter string="Academic Year" name="group_by_academic_year" context="{'group_by': 'current_academic_year_id'}"/>
                    <filter string="Program" name="group_by_program" context="{'group_by': 'current_program_id'}"/>
                    <filter string="Standard/Class" name="group_by_standard" context="{'group_by': 'current_standard_id'}"/>
                    <filter string="Section" name="group_by_section" context="{'group_by': 'current_section_id'}"/>
                    <filter string="Age Group" name="group_by_age_group" context="{'group_by': 'age_group'}"/>
                    <filter string="Admission Date" name="group_by_admission_date" context="{'group_by': 'admission_date'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Kanban View -->
    <record id="view_student_kanban" model="ir.ui.view">
        <field name="name">edu.student.kanban</field>
        <field name="model">edu.student</field>
        <field name="arch" type="xml">
            <kanban default_group_by="state" class="o_kanban_small_column">
                <field name="id"/>
                <field name="name"/>
                <field name="student_id"/>
                <field name="image_128"/>
                <field name="state"/>
                <field name="age"/>
                <field name="gender"/>
                <field name="current_program_id"/>
                <field name="current_section_id"/>
                <field name="overall_gpa"/>
                <field name="is_birthday_today"/>
                <templates>
                    <t t-name="kanban-box">
                        <div class="oe_kanban_card oe_kanban_global_click">
                            <div class="o_kanban_image">
                                <img t-att-src="kanban_image('edu.student', 'image_128', record.id.raw_value)" alt="Student"/>
                            </div>
                            <div class="oe_kanban_details">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings">
                                        <strong class="o_kanban_record_title">
                                            <field name="name"/>
                                        </strong>
                                        <br/>
                                        <small class="o_kanban_record_subtitle text-muted">
                                            <field name="student_id"/>
                                        </small>
                                    </div>
                                </div>
                                <div class="o_kanban_record_body">
                                    <div class="row">
                                        <div class="col-6">
                                            <span class="fa fa-user"/> <field name="age"/> years
                                        </div>
                                        <div class="col-6">
                                            <span class="fa fa-venus-mars"/> <field name="gender"/>
                                        </div>
                                    </div>
                                    <div t-if="record.current_program_id.raw_value">
                                        <span class="fa fa-graduation-cap"/> <field name="current_program_id"/>
                                    </div>
                                    <div t-if="record.current_section_id.raw_value">
                                        <span class="fa fa-users"/> <field name="current_section_id"/>
                                    </div>
                                    <div t-if="record.overall_gpa.raw_value">
                                        <span class="fa fa-star"/> GPA: <field name="overall_gpa"/>
                                    </div>
                                </div>
                                <div class="o_kanban_record_bottom">
                                    <div class="oe_kanban_bottom_left">
                                        <span t-if="record.is_birthday_today.raw_value" class="fa fa-birthday-cake text-warning" title="Birthday Today!"/>
                                    </div>
                                    <div class="oe_kanban_bottom_right">
                                        <span t-att-class="'badge badge-' + (record.state.raw_value == 'active' ? 'success' : record.state.raw_value == 'enrolled' ? 'info' : record.state.raw_value == 'graduated' ? 'primary' : 'secondary')">
                                            <field name="state"/>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- Calendar View -->
    <record id="view_student_calendar" model="ir.ui.view">
        <field name="name">edu.student.calendar</field>
        <field name="model">edu.student</field>
        <field name="arch" type="xml">
            <calendar string="Students" date_start="birth_date" color="state" mode="month">
                <field name="name"/>
                <field name="student_id"/>
                <field name="age"/>
                <field name="state"/>
            </calendar>
        </field>
    </record>

    <!-- Action -->
    <record id="action_edu_student" model="ir.actions.act_window">
        <field name="name">Students</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">edu.student</field>
        <field name="view_mode">kanban,list,form,calendar</field>
        <field name="context">{'search_default_active_students': 1}</field>
        <field name="help" type="html">
            <div class="o_view_nocontent_edu edu_fade_in">
                <!-- Welcome Section -->
                <div class="edu_help_welcome">
                    <h1 class="edu_help_title">🎓 Student Management</h1>
                    <p class="edu_help_subtitle">Every great journey begins with a single step!</p>
                    <p class="edu_help_tagline">
                        <strong>Building Tomorrow's Leaders</strong><br/>
                        Start building your educational community by adding your first student. This is where dreams take shape and futures are molded.
                    </p>
                </div>

                <!-- Quick Start Steps -->
                <div class="edu_progress_steps">
                    <div class="edu_step">
                        <div class="edu_step_icon">👤</div>
                        <div class="edu_step_text">Add Profile</div>
                    </div>
                    <div class="edu_step">
                        <div class="edu_step_icon">📋</div>
                        <div class="edu_step_text">Fill Details</div>
                    </div>
                    <div class="edu_step">
                        <div class="edu_step_icon">🎓</div>
                        <div class="edu_step_text">Enroll</div>
                    </div>
                    <div class="edu_step">
                        <div class="edu_step_icon">📊</div>
                        <div class="edu_step_text">Track Progress</div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="edu_help_actions">
                    <a href="#" class="edu_action_btn btn-primary" onclick="this.createNewRecord()">
                        <i class="fa fa-plus"></i>
                        Add New Student
                    </a>
                    <a href="#" class="edu_action_btn btn-success" onclick="this.showQuickStart()">
                        <i class="fa fa-rocket"></i>
                        Quick Start Guide
                    </a>
                    <a href="/documentation/student-management" class="edu_action_btn btn-outline-secondary" target="_blank">
                        <i class="fa fa-book"></i>
                        Documentation
                    </a>
                </div>

                <!-- Enhanced Features Alert -->
                <div class="alert edu_alert_enhanced alert-info edu_slide_up">
                    <h5>
                        <i class="fa fa-bullseye"></i>
                        🌟 What you can manage here:
                    </h5>
                    <ul class="mb-0">
                        <li>📝 Complete student profiles with photos and personal details</li>
                        <li>🎯 Academic enrollment, grades, and performance tracking</li>
                        <li>🏥 Health records, allergies, and special accommodation needs</li>
                        <li>👨‍👩‍👧‍👦 Guardian contacts and emergency information</li>
                        <li>🌍 International student documentation and visa status</li>
                        <li>🏆 Achievements, awards, and extracurricular activities</li>
                        <li>📱 Digital portfolios and learning analytics</li>
                        <li>🔔 Automated notifications and communication</li>
                    </ul>
                    <div class="mt-3">
                        <button class="btn btn-sm btn-outline-primary" onclick="this.copyTips()">
                            <i class="fa fa-copy"></i> Copy Tips
                        </button>
                        <button class="btn btn-sm btn-outline-info ml-2" onclick="this.showExamples()">
                            <i class="fa fa-lightbulb-o"></i> Examples
                        </button>
                        <button class="btn btn-sm btn-outline-success ml-2" onclick="this.importStudents()">
                            <i class="fa fa-upload"></i> Import Students
                        </button>
                    </div>
                </div>

                <!-- Inspirational Quote -->
                <div class="edu_help_quote">
                    <em>"Education is the most powerful weapon which you can use to change the world."</em>
                    <div class="text-right mt-2">
                        <small>- Nelson Mandela</small>
                    </div>
                </div>
            </div>
        </field>
    </record>

    <!-- Dashboard Action for Active Students -->
    <record id="action_active_students_dashboard" model="ir.actions.act_window">
        <field name="name">Active Students</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">edu.student</field>
        <field name="view_mode">kanban,list,form</field>
        <field name="domain">[('state', '=', 'active')]</field>
        <field name="context">{'create': False}</field>
    </record>

    <!-- Birthday Students Action -->
    <record id="action_birthday_students" model="ir.actions.act_window">
        <field name="name">Birthday Students</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">edu.student</field>
        <field name="view_mode">kanban,list</field>
        <field name="domain">[('is_birthday_today', '=', True)]</field>
        <field name="context">{'create': False}</field>
    </record>

</odoo>