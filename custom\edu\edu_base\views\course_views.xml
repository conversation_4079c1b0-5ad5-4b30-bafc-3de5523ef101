<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <!-- List View -->
    <record id="view_edu_course_list" model="ir.ui.view">
        <field name="name">edu.course.list</field>
        <field name="model">edu.course</field>
        <field name="arch" type="xml">
            <list string="Courses">
                <field name="code"/>
                <field name="name"/>
                <field name="course_type"/>
                <field name="credits"/>
                <field name="duration_hours" optional="show"/>
                <field name="standard_ids" widget="many2many_tags" optional="show"/>
                <field name="active" optional="hide"/>
            </list>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_edu_course_form" model="ir.ui.view">
        <field name="name">edu.course.form</field>
        <field name="model">edu.course</field>
        <field name="arch" type="xml">
            <form string="Course">
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <widget name="web_ribbon" title="Archived" invisible="active"/>
                        <field name="active" invisible="1"/>
                    </div>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Course Name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="code"/>
                            <field name="course_type"/>
                            <field name="credits"/>
                        </group>
                        <group>
                            <field name="duration_hours"/>
                            <field name="standard_ids" widget="many2many_tags"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Description" name="description">
                            <field name="description" placeholder="Enter detailed course description..."/>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- Search View -->
    <record id="view_edu_course_search" model="ir.ui.view">
        <field name="name">edu.course.search</field>
        <field name="model">edu.course</field>
        <field name="arch" type="xml">
            <search string="Search Courses">
                <field name="name"/>
                <field name="code"/>
                <field name="standard_ids"/>
                <separator/>
                <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                <group expand="0" string="Group By">
                    <filter string="Course Type" name="group_by_type" domain="[]" context="{'group_by': 'course_type'}"/>
                    <filter string="Credits" name="group_by_credits" domain="[]" context="{'group_by': 'credits'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action -->
    <record id="action_edu_course" model="ir.actions.act_window">
        <field name="name">Courses</field>
        <field name="res_model">edu.course</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="view_edu_course_search"/>
        <field name="help" type="html">
            <div class="o_view_nocontent_edu edu_fade_in">
                <!-- Welcome Section -->
                <div class="edu_help_welcome">
                    <h1 class="edu_help_title">📚 Course Management</h1>
                    <p class="edu_help_subtitle">Knowledge begins with a single course!</p>
                    <p class="edu_help_tagline">
                        <strong>Designing Learning Experiences</strong><br/>
                        Design the building blocks of education by creating comprehensive courses that will inspire and educate students across different standards and levels.
                    </p>
                </div>

                <!-- Quick Start Steps -->
                <div class="edu_progress_steps">
                    <div class="edu_step">
                        <div class="edu_step_icon">📚</div>
                        <div class="edu_step_text">Create Course</div>
                    </div>
                    <div class="edu_step">
                        <div class="edu_step_icon">🎯</div>
                        <div class="edu_step_text">Set Objectives</div>
                    </div>
                    <div class="edu_step">
                        <div class="edu_step_icon">👨‍🏫</div>
                        <div class="edu_step_text">Assign Faculty</div>
                    </div>
                    <div class="edu_step">
                        <div class="edu_step_icon">🚀</div>
                        <div class="edu_step_text">Launch</div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="edu_help_actions">
                    <a href="#" class="edu_action_btn btn-primary" onclick="this.createNewRecord()">
                        <i class="fa fa-plus"></i>
                        Create New Course
                    </a>
                    <a href="#" class="edu_action_btn btn-success" onclick="this.showQuickStart()">
                        <i class="fa fa-rocket"></i>
                        Quick Start Guide
                    </a>
                    <a href="/documentation/course-management" class="edu_action_btn btn-outline-secondary" target="_blank">
                        <i class="fa fa-book"></i>
                        Documentation
                    </a>
                </div>

                <!-- Enhanced Features Alert -->
                <div class="alert edu_alert_enhanced alert-info edu_slide_up">
                    <h5>
                        <i class="fa fa-bullseye"></i>
                        📖 What you can create:
                    </h5>
                    <ul class="mb-0">
                        <li>📝 Detailed course descriptions and learning objectives</li>
                        <li>🎯 Course prerequisites and skill requirements</li>
                        <li>⏱️ Duration, credit hours, and assessment methods</li>
                        <li>👨‍🏫 Faculty assignments and teaching resources</li>
                        <li>📊 Cross-standard course offerings and pathways</li>
                        <li>🏆 Certification and completion criteria</li>
                        <li>📚 Resource allocation and material management</li>
                        <li>📈 Performance tracking and analytics</li>
                    </ul>
                    <div class="mt-3">
                        <button class="btn btn-sm btn-outline-primary" onclick="this.copyTips()">
                            <i class="fa fa-copy"></i> Copy Tips
                        </button>
                        <button class="btn btn-sm btn-outline-info ml-2" onclick="this.showExamples()">
                            <i class="fa fa-lightbulb-o"></i> Examples
                        </button>
                        <button class="btn btn-sm btn-outline-success ml-2" onclick="this.importCourses()">
                            <i class="fa fa-upload"></i> Import Courses
                        </button>
                    </div>
                </div>

                <!-- Inspirational Quote -->
                <div class="edu_help_quote">
                    <em>"Live as if you were to die tomorrow. Learn as if you were to live forever."</em>
                    <div class="text-right mt-2">
                        <small>- Mahatma Gandhi</small>
                    </div>
                </div>
            </div>
        </field>
    </record>
</odoo>